<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="15dp">

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_validity"
        android:layout_alignStart="@+id/tv_validity"
        android:layout_marginTop="5.5dp"
        android:layout_marginEnd="10dp">

        <RelativeLayout
            android:id="@+id/rl_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="4dp"
                android:textColor="@color/color_FF2121"
                android:textSize="15dp"
                android:textStyle="bold"
                tools:text="¥15.00" />

            <TextView
                android:id="@+id/tv_coupon_discount_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/tv_price"
                android:singleLine="true"
                android:textColor="#ff2121"
                android:textSize="@dimen/dimen_dp_10"
                android:visibility="gone"
                tools:text="券后立减2.12起"
                tools:visibility="visible" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_buy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="4dp"
                android:text="领券购买"
                android:textColor="@color/white"
                android:textSize="12dp"
                android:visibility="gone"
                app:rv_backgroundColor="@color/color_00B377"
                app:rv_cornerRadius="12.5dp"
                tools:visibility="visible" />

            <com.ybmmarket20.view.ProductEditLayout
                android:id="@+id/el_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="8dp"
                android:layout_marginEnd="0dp"
                android:paddingLeft="10dp"
                android:paddingTop="4dp"
                android:paddingRight="10dp"
                android:paddingBottom="4dp"
                android:visibility="gone"
                tools:visibility="gone" />

        </RelativeLayout>

        # 价格认证资质可见

        <TextView
            android:id="@+id/tv_price_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_tv_live_goods_list_dialog_price_tips"
            android:textColor="@color/color_ff982c"
            android:textSize="14dp"
            tools:visibility="gone"
            android:visibility="gone" />

    </FrameLayout>

    <ImageView
        android:id="@+id/iv_pic"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_marginStart="10dp"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBaseline="@+id/tv_validity"
        android:layout_marginStart="5dp"
        android:layout_toEndOf="@+id/tv_validity"
        android:textColor="@color/text_676773"
        android:textSize="12dp"
        tools:text="2021.07.01/2021.09.01" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="19.5dp"
        android:layout_toEndOf="@+id/iv_pic"
        android:textColor="@color/text_292933"
        android:textSize="16dp"
        tools:text="念慈菴 蜜炼川贝枇杷膏多出可以折行/12颗*1盒" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@+id/fl_container"
        android:layout_alignStart="@+id/tv_title"
        android:layout_marginTop="17.5dp"
        android:layout_marginEnd="10dp"
        android:background="@color/colors_f5f5f5" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_validity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_title"
        android:layout_alignStart="@+id/tv_title"
        android:layout_marginTop="6.5dp"
        android:paddingLeft="2.5dp"
        android:paddingTop="0.5dp"
        android:paddingRight="2.5dp"
        android:paddingBottom="0.5dp"
        android:text="@string/str_tv_live_goods_list_dialog_validity"
        android:textColor="@color/text_676773"
        android:textSize="10dp"
        app:rv_backgroundColor="@color/color_FAFAFA"
        app:rv_cornerRadius="1dp"
        app:rv_strokeColor="@color/color_D1D1D6"
        app:rv_strokeWidth="0.5dp" />

</RelativeLayout>
