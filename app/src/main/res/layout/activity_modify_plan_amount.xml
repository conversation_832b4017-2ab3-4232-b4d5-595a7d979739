<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f8f8f8"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:orientation="vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="90dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="产品名称："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_product_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="#292933"
                android:textSize="16sp"
                tools:text="达琦再造丸" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="90dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="规格："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_product_spec"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="#292933"
                android:textSize="16sp"
                tools:text="12粒*3/盒" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="90dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="厂家："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_manufacturer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="#292933"
                android:textSize="16sp"
                tools:text="制药我们是认真的十厂" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#eeeeee" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <TextView
                android:layout_width="90dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="数量："
                android:textColor="#AAAAAA"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/et_product_num"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:cursorVisible="true"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:inputType="number"
                android:maxLength="7"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#292933"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
