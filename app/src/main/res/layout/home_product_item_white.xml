<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="118dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center_horizontal"
    android:minWidth="178dp"
    android:padding="2dp">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/rll_contain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:minWidth="178dp"
        android:orientation="vertical"
        android:paddingBottom="4dp"
        app:rv_cornerRadius="4dp">

        <FrameLayout
            android:layout_width="104dp"
            android:layout_height="104dp">

            <ImageView
                android:id="@+id/iv_product"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:layout_marginTop="2dp"
                android:padding="@dimen/home_product_image_padding" />

            <ImageView
                android:id="@+id/iv_tag_left"
                android:layout_width="104dp"
                android:layout_height="104dp"
                android:layout_marginTop="2dp"
                android:scaleType="fitXY"
                android:src="@drawable/transparent" />

            <com.ybmmarket20.view.PromotionTagView
                android:id="@+id/view_ptv"
                android:layout_width="104dp"
                app:subTitleTextSize="4dp"
                app:contentTextSize="8dp"
                android:layout_height="104dp" />

            <TextView
                android:id="@+id/tv_activity_price"
                style="@style/activity_price"
                android:layout_height="13dp"
                android:text=""
                android:visibility="gone" />

        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="6dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/tv_procurement_festival"
                android:layout_width="42dp"
                android:layout_height="17dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:visibility="gone"
                android:background="@drawable/icon_procurement_festival" />

            <TextView
                android:id="@+id/tv_exclusive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_exclusive"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="独家"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_health_insurance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="2dp"
                android:background="@drawable/bg_brand_item_health_insurance"
                android:paddingLeft="2dp"
                android:paddingRight="2dp"
                android:text="医保"
                android:textColor="@color/white"
                android:textSize="11sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text=""
                tools:text="商品名称"
                android:textColor="#ff000000"
                android:textSize="12sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_spec"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:paddingLeft="7dp"
            android:singleLine="true"
            tools:text="14g"
            android:textColor="@color/text_9494A6"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:paddingBottom="2dp"
            android:paddingLeft="7dp"
            android:paddingTop="2dp"
            android:singleLine="true"
            android:text="¥"
            android:textColor="@color/record_red"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_audit_passed_visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/audit_passed_visible"
            android:visibility="gone"
            android:textColor="@color/detail_tv_FF982C"
            android:layout_marginTop="@dimen/dimen_dp_7"
            android:paddingLeft="@dimen/dimen_dp_7"
            android:textSize="@dimen/dimen_dp_12" />

        <com.ybmmarket20.view.TagView
            android:id="@+id/rl_icon_type"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_marginLeft="4.8dp"/>
    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <ImageView
        android:id="@+id/home_time_bg"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:layout_marginBottom="35dp"
        android:src="@drawable/home_time_bg"
        android:visibility="invisible" />

</FrameLayout>