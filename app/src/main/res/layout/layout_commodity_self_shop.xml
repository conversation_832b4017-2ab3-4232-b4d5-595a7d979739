<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_89"
    android:background="@drawable/icon_commodity_self_shop_bg">

    <ImageView
        android:id="@+id/iv_self_shop_logo"
        android:layout_width="@dimen/dimen_dp_47"
        android:layout_height="@dimen/dimen_dp_47"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:src="@drawable/icon_goods_detail_shop_logo_default"
        android:layout_marginTop="@dimen/dimen_dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_self_shop_name"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_9"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/rt_self_shop_open"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/iv_self_shop_logo"
        app:layout_constraintTop_toTopOf="@+id/iv_self_shop_logo"
        tools:text="店铺名" />

    <TextView
        android:id="@+id/tv_self_put_away"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_9"
        android:textColor="@color/color_676773"
        android:textSize="@dimen/dimen_dp_11"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="@+id/iv_self_shop_logo"
        app:layout_constraintStart_toEndOf="@+id/iv_self_shop_logo"
        tools:text="上架5种" />

    <com.ybmmarket20.view.ShopNameWithTagView
        android:id="@+id/snwtv_commodity_self_shop"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_self_shop_logo"
        app:layout_constraintTop_toBottomOf="@+id/iv_self_shop_logo" />

    <TextView
        android:id="@+id/rt_self_shop_open"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_30"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:background="@drawable/shape_goods_detail_entry_btn_pop"
        android:drawableEnd="@drawable/icon_goods_detail_entry_shop_pop_arrow"
        android:gravity="center"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_7"
        android:text="进店逛逛"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_self_shop_name"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>