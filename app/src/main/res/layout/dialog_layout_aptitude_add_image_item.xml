<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFE7E7ED"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="108dp"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="0dp">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_taking_pictures"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="拍摄"
            android:textColor="#FF000000"
            android:textSize="18dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:background="#F4F4F4" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_photo_gallery"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/white"
            android:gravity="center"
            android:text="从手机相册选择"
            android:textColor="#FF000000"
            android:textSize="18dp" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:gravity="center"
        android:layout_marginTop="7dp"
        android:text="取消"
        android:textColor="#FF292933"
        android:textSize="18dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="0dp" />

</LinearLayout>