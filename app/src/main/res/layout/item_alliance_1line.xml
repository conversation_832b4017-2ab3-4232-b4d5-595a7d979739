<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/normal_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alliance_text_margin_left"
                android:layout_marginTop="9dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:text="病        因"
                    android:textColor="#676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_changshi"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/color_292933"
                    android:textSize="14sp"
                    tools:text="其起因多是劳累，没休息好，再加上吹风或受就的撒金刚萨埵" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alliance_text_margin_left"
                android:layout_marginTop="9dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="20dp"
                    android:text="常见症状"
                    android:textColor="#676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_changjianzhengzhuang"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/color_292933"
                    android:textSize="14sp"
                    tools:text="其起因多是劳累，没休息好，再加上吹风或受就的撒金刚萨埵" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/layout_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alliance_text_margin_left"
                android:orientation="vertical"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="20dp"
                        android:text="温馨提示"
                        android:textColor="#676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_jianyiguke"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/color_292933"
                        android:textSize="14sp"
                        tools:text="其起因多是劳累，没休息好，再加上吹风或受就的撒金刚萨埵" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="20dp"
                        android:text="治疗原则"
                        android:textColor="#676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_yongyaoyuanze"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/color_292933"
                        android:textSize="14sp"
                        tools:text="其起因多是劳累，没休息好，再加上吹风或受就的撒金刚萨埵" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="20dp"
                        android:text="常见用药"
                        android:textColor="#676773"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_changjianyongyao"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="8"
                        android:textColor="@color/color_292933"
                        android:textSize="14sp"
                        tools:text="其起因多是劳累，没休息好，再加上吹风或受就的撒金刚萨埵" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="26dp"
            android:text="常见组合"
            android:textColor="@color/color_292933"
            android:textStyle="bold"
            android:textSize="16sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recommend"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:layout_marginTop="18dp">

            <TextView
                android:id="@+id/tv_goods_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:text="共计3种药品"
                android:textColor="@color/color_292933"
                android:textSize="14sp" />

            <com.ybmmarket20.common.widget.RoundTextView
                android:id="@+id/tv_batch_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:paddingLeft="20dp"
                android:paddingTop="4dp"
                android:paddingRight="20dp"
                android:paddingBottom="4dp"
                android:text="一键加购"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:rv_backgroundColor="#00B377"
                app:rv_cornerRadius="2dp" />
        </RelativeLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="33dp"
        android:background="@color/white" />

</LinearLayout>