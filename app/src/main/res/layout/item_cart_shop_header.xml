<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_shop_header"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_dp_10"
    android:background="@drawable/bg_corner_all"
    android:paddingBottom="@dimen/dimen_dp_14">

    <View
        android:id="@+id/line_top"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_3"
        android:background="#A4DABB"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <CheckBox
        android:id="@+id/cb_shop"
        android:layout_width="@dimen/dimen_dp_26"
        android:layout_height="@dimen/dimen_dp_26"
        android:layout_marginLeft="@dimen/dimen_dp_4"
        android:background="@drawable/selector_cart_check"
        android:button="@null"
        app:layout_constraintBottom_toTopOf="@id/line_middle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top"
        tools:checked="true" />

    <TextView
        android:id="@+id/tv_shop_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_5"
        android:layout_marginRight="@dimen/dimen_dp_5"
        android:drawableRight="@drawable/icon_cart_shop_arrow"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:maxWidth="210dp"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/line_middle"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toRightOf="@id/cb_shop"
        app:layout_constraintRight_toLeftOf="@id/iv_coupon"
        app:layout_constraintTop_toBottomOf="@id/line_top"
        tools:text="药帮忙 药帮忙 药帮忙 药帮忙 药帮忙 药帮忙 药帮忙" />


    <ImageView
        android:id="@+id/iv_coupon"
        android:layout_width="@dimen/dimen_dp_35"
        android:layout_height="@dimen/dimen_dp_19"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="@drawable/icon_cart_shop_coupon"
        app:layout_constraintBottom_toTopOf="@id/line_middle"
        app:layout_constraintRight_toLeftOf="@id/line_vertical"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <View
        android:id="@+id/line_vertical"
        android:layout_width="0.5dp"
        android:layout_height="@dimen/dimen_dp_14"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="#D8D8D8"
        app:layout_constraintBottom_toTopOf="@id/line_middle"
        app:layout_constraintRight_toLeftOf="@id/cb_expand"
        app:layout_constraintTop_toBottomOf="@id/line_top" />

    <CheckBox
        android:id="@+id/cb_expand"
        android:layout_width="@dimen/dimen_dp_43"
        android:layout_height="@dimen/dimen_dp_19"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:background="@drawable/selector_cart_expand_collapse"
        android:button="@null"
        app:layout_constraintBottom_toTopOf="@id/line_middle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line_top" />


    <View
        android:id="@+id/line_middle"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dimen_dp_39"
        android:background="#EEEEEE"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_freight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="3dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_14"
        android:drawablePadding="4dp"
        android:ellipsize="end"
        android:maxWidth="300dp"
        android:maxLines="1"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/text_replenishment_12sp"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/cb_shop"
        app:layout_constraintTop_toBottomOf="@id/line_middle"
        tools:drawableRight="@drawable/icon_hint_image_cart"
        tools:text="已满300元起送" />

    <TextView
        android:id="@+id/tv_freight_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_14"
        android:drawableRight="@drawable/icon_cart_arrow"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/text_replenishment_12sp"
        android:visibility="gone"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_freight"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="去凑单" />


    <TextView
        android:id="@+id/tv_refund"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginStart="3dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/text_replenishment_12sp"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="@id/cb_shop"
        app:layout_constraintRight_toLeftOf="@id/tv_refund_arrow"
        app:layout_constraintTop_toBottomOf="@id/tv_freight"
        tools:text="每满200元，4个品种，返6元优惠券一张,每满200元，4个品种，返6元优惠券一张" />

    <TextView
        android:id="@+id/tv_refund_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_14"
        android:drawableRight="@drawable/icon_cart_arrow"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/text_replenishment_12sp"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_refund"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="去凑单" />

    <!--店铺优惠的去凑单-->
    <ImageView
        android:id="@+id/iv_shop_discounts"
        android:layout_width="48dp"
        android:layout_marginStart="3dp"
        android:src="@drawable/icon_cart_shop_discounts"
        app:layout_constraintTop_toTopOf="@id/tv_shop_discounts"
        app:layout_constraintBottom_toBottomOf="@id/tv_shop_discounts"
        app:layout_constraintStart_toStartOf="@id/cb_shop"
        tools:visibility="gone"
        android:visibility="visible"
        app:layout_constraintEnd_toStartOf="@id/tv_shop_discounts"
        android:layout_height="12dp"/>

    <TextView
        android:id="@+id/tv_shop_discounts"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_color_333333"
        android:textSize="@dimen/text_replenishment_12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@id/iv_shop_discounts"
        android:layout_marginStart="6dp"
        app:layout_constraintEnd_toStartOf="@id/tv_shop_discounts_arrow"
        app:layout_constraintTop_toBottomOf="@id/tv_refund"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="每满200元，4个品种，返6元优惠券一张,每满200元，4个品种，返6元优惠券一张" />

    <TextView
        android:id="@+id/tv_shop_discounts_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_14"
        android:drawableRight="@drawable/icon_cart_arrow"
        android:drawablePadding="@dimen/dimen_dp_4"
        android:textColor="@color/text_color_333333"
        android:visibility="gone"
        android:textSize="@dimen/text_replenishment_12sp"
        tools:visibility="visible"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_shop_discounts"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="去凑单" />

</androidx.constraintlayout.widget.ConstraintLayout>