<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_back"
                style="@style/header_layout_left"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_back" />


            <TextView
                android:id="@+id/tv_title"
                style="@style/header_layout_mid"
                android:layout_gravity="center"
                android:text="协议" />
        </FrameLayout>

        <WebView
            android:id="@+id/wv_webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</FrameLayout>