<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_pop_title"
            android:layout_width="0dp"
            android:layout_height="43dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:text="起送包邮金额提醒"
                android:textColor="@color/color_292933"
                android:textSize="@dimen/dimen_dp_16"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="@dimen/dimen_dp_22"
                android:layout_height="@dimen/dimen_dp_22"
                android:layout_marginRight="@dimen/dimen_dp_14"
                android:src="@drawable/icon_close"
                app:layout_constraintBottom_toBottomOf="@id/tv_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_title" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/ll_package"
            android:layout_width="0dp"
            android:layout_height="300dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_pop_title">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_freight"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:itemCount="5"
                tools:listitem="@layout/item_package_gather" />

        </FrameLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/btn_left"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="@dimen/dimen_dp_16"
            android:gravity="center"
            android:text="继续加购"
            android:textColor="@color/color_292933"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/btn_right"
            app:layout_constraintTop_toBottomOf="@id/ll_package"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            app:rv_strokeColor="@color/colors_E4E4EB"
            app:rv_strokeWidth="1dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/btn_right"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginRight="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dimen_dp_16"
            android:gravity="center"
            android:text="暂不凑单，去结算"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/btn_left"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_package"
            app:rv_backgroundColor="@color/base_colors"
            app:rv_cornerRadius="2dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/btn_only"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="@dimen/dimen_dp_16"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_package"
            app:rv_backgroundColor="@color/color_00b377"
            app:rv_cornerRadius="2dp"
            app:rv_strokeWidth="1dp"
            android:text="继续加购" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/btn_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="btn_left,btn_right" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>