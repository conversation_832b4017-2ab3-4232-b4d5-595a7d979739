<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rll_shop_qualification"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:orientation="horizontal">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtv_company_icon"
            android:layout_width="2dp"
            android:layout_height="@dimen/dimen_dp_13"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/color_00b377"
            app:rv_cornerRadius="@dimen/dimen_dp_1" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:text="企业信息"
            android:textColor="#191919"
            android:textSize="@dimen/dimen_dp_14"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/rtv_company_icon"
            app:layout_constraintStart_toEndOf="@+id/rtv_company_icon"
            app:layout_constraintTop_toTopOf="@+id/rtv_company_icon" />

        <TextView
            android:id="@+id/tv_item_entry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:textSize="@dimen/dimen_dp_12"
            android:text="商家开户说明"
            android:textColor="@color/color_00b377"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/rtv_company_icon"
            app:layout_constraintTop_toTopOf="@+id/rtv_company_icon" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/v_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/color_f7f7f8" />
</LinearLayout>