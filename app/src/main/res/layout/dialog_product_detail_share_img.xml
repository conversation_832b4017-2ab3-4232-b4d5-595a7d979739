<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/rcl_share_img"
        android:layout_width="@dimen/dimen_dp_300"
        android:layout_height="450dp"
        android:layout_marginBottom="@dimen/dimen_dp_15"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toTopOf="@+id/rcl_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="#01B377"
        app:rv_cornerRadius="@dimen/dimen_dp_8">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtv_bg"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_7" />

        <ImageView
            android:id="@+id/iv_share_shop_name"
            android:layout_width="@dimen/dimen_dp_220"
            android:layout_height="@dimen/dimen_dp_30"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:scaleType="fitXY"
            android:src="@drawable/icon_share_img_shop_name_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_shop_name"
            android:layout_width="@dimen/dimen_dp_150"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_14"
            android:textColor="#F6FC50"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/iv_share_shop_name"
            app:layout_constraintEnd_toEndOf="@+id/iv_share_shop_name"
            app:layout_constraintStart_toStartOf="@+id/iv_share_shop_name"
            app:layout_constraintTop_toTopOf="@+id/iv_share_shop_name"
            tools:text="长沙小药药自营旗舰店" />

        <ImageView
            android:id="@+id/iv_goods_img"
            android:layout_width="@dimen/dimen_dp_220"
            android:layout_height="@dimen/dimen_dp_200"
            android:layout_marginTop="@dimen/dimen_dp_20"
            app:layout_constraintBottom_toBottomOf="@+id/rtv_bg"
            app:layout_constraintEnd_toEndOf="@+id/rtv_bg"
            app:layout_constraintStart_toStartOf="@+id/rtv_bg"
            app:layout_constraintTop_toTopOf="@+id/rtv_bg"
            tools:src="@drawable/image_placeholder" />

        <ImageView
            android:id="@+id/iv_marker"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0"
            app:layout_constraintBottom_toBottomOf="@+id/iv_goods_img"
            app:layout_constraintEnd_toEndOf="@+id/iv_goods_img"
            app:layout_constraintStart_toStartOf="@+id/iv_goods_img"
            app:layout_constraintTop_toTopOf="@+id/iv_goods_img" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/iv_qrcode_bg"
            android:layout_width="@dimen/dimen_dp_75"
            android:layout_height="@dimen/dimen_dp_75"
            android:layout_marginTop="@dimen/dimen_dp_12"
            android:layout_marginEnd="@dimen/dimen_dp_4"
            app:layout_constraintEnd_toEndOf="@+id/rtv_bg"
            app:layout_constraintTop_toBottomOf="@+id/rtv_bg"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_6" />

        <ImageView
            android:id="@+id/iv_qrcode"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_0"
            android:layout_margin="@dimen/dimen_dp_3"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toBottomOf="@+id/iv_qrcode_bg"
            app:layout_constraintEnd_toEndOf="@+id/iv_qrcode_bg"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="@+id/iv_qrcode_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_qrcode_bg"
            app:layout_constraintVertical_weight="1"
            tools:src="@drawable/image_placeholder" />

        <TextView
            android:id="@+id/tv_qrcode_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_10"
            android:text="长按二维码查看"
            android:layout_marginTop="@dimen/dimen_dp_3"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="@+id/iv_qrcode_bg"
            app:layout_constraintStart_toStartOf="@+id/iv_qrcode_bg"
            app:layout_constraintTop_toBottomOf="@+id/iv_qrcode_bg" />


        <TextView
            android:id="@+id/tv_goods_name"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_16"
            android:textColor="@color/white"
            android:paddingEnd="@dimen/dimen_dp_16"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintEnd_toStartOf="@+id/iv_qrcode_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_qrcode_bg"
            tools:text="20盒包邮 " />

        <TextView
            android:id="@+id/tv_goods_name1"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_16"
            android:textColor="@color/white"
            android:paddingEnd="@dimen/dimen_dp_16"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintEnd_toStartOf="@+id/iv_qrcode_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_qrcode_bg"
            tools:text="20盒包邮 " />

        <TextView
            android:id="@+id/rtv_tip_bg"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_28"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:background="@drawable/shape_share_tip"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_qrcode_des" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="详情以登录显示为准"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_11"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/rtv_tip_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/rtv_tip_bg" />

        <View
            android:id="@+id/v_line_left"
            android:layout_width="@dimen/dimen_dp_20"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="@drawable/icon_share_img_tip_left"
            android:layout_marginEnd="@dimen/dimen_dp_8"
            app:layout_constraintBottom_toBottomOf="@+id/tv_tip"
            app:layout_constraintEnd_toStartOf="@+id/tv_tip"
            app:layout_constraintTop_toTopOf="@+id/tv_tip" />

        <View
            android:id="@+id/v_line_right"
            android:layout_width="@dimen/dimen_dp_20"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="@drawable/icon_share_img_tip_right"
            android:layout_marginStart="@dimen/dimen_dp_8"
            app:layout_constraintBottom_toBottomOf="@+id/tv_tip"
            app:layout_constraintStart_toEndOf="@+id/tv_tip"
            app:layout_constraintTop_toTopOf="@+id/tv_tip" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_textview"
            android:gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            app:layout_constraintBottom_toTopOf="@+id/rtv_tip_bg"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tv_after_sale"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_10"
                android:paddingTop="@dimen/dimen_dp_3"
                android:gravity="center"
                android:paddingStart="@dimen/dimen_dp_3"
                android:background="@drawable/icon_goods_share_after_sale"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="折后价：¥20.00" />

            <TextView
                android:id="@+id/tv_control_un"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_10"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dimen_dp_3"
                android:paddingStart="@dimen/dimen_dp_5"
                android:layout_marginLeft="@dimen/dimen_dp_3"
                android:background="@drawable/icon_goods_share_control_un"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_after_sale"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="签署协议后可买" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_21"
            android:textColor="@color/white"
            app:layout_constraintBottom_toTopOf="@+id/cl_textview"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="¥23.50/盒" />

    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/rcl_bottom"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="300dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dimen_dp_5">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="分享到"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_16"
            android:textStyle="bold"
            android:layout_marginTop="@dimen/dimen_dp_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvShare"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_2"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:spanCount="3"
            tools:itemCount="6"
            tools:listitem="@layout/item_share_product" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtv_cancel"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_50"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_marginBottom="@dimen/dimen_dp_10"
            android:text="取消"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_16"
            android:layout_marginTop="@dimen/dimen_dp_25"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_share_img"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="@dimen/dimen_dp_2" />


    </com.ybmmarket20.common.widget.RoundConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>