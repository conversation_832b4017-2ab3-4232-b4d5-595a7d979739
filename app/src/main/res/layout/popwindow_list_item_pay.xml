<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="@dimen/dimen_dp_6">

    <com.ybmmarket20.common.widget.RoundFrameLayout
        android:id="@+id/rflBg"
        android:layout_width="@dimen/dimen_dp_77"
        android:layout_height="@dimen/dimen_dp_77"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="#F2F2F2"
        app:rv_cornerRadius="@dimen/dimen_dp_2" />

    <ImageView
        android:id="@+id/ivGoods"
        android:layout_width="@dimen/dimen_dp_67"
        android:layout_height="@dimen/dimen_dp_67"
        android:layout_margin="@dimen/dimen_dp_5"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintEnd_toEndOf="@+id/rflBg"
        app:layout_constraintStart_toStartOf="@+id/rflBg"
        app:layout_constraintTop_toTopOf="@+id/rflBg"
        tools:src="@drawable/logo" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:ellipsize="end"
        android:lineHeight="@dimen/dimen_dp_13"
        android:maxLines="2"
        android:text="感康 复方氨酚烷胺片复方氨酚烷胺片/0.8g*8s*4板 薄膜衣"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rflBg"
        app:layout_constraintTop_toTopOf="@+id/rflBg" />

    <TextView
        android:id="@+id/tvEffect"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:text="有效期至：2025-01-27"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rflBg"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_19"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintStart_toEndOf="@+id/rflBg"
        tools:text="¥16.90" />

    <TextView
        android:id="@+id/tvPriceAfterDiscount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintStart_toEndOf="@+id/tvPrice"
        tools:text="折后约￥13.50" />

    <TextView
        android:id="@+id/tvInventory"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintBottom_toBottomOf="@+id/rflBg"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="库存 500" />

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="#EEEEEE"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rflBg" />

    <LinearLayout
        android:id="@+id/llDiscount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/divider">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="优惠:"
            android:textColor="@color/color_333"
            android:textSize="@dimen/dimen_dp_12"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDiscountTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_10"
            android:paddingStart="@dimen/dimen_dp_3"
            android:paddingEnd="@dimen/dimen_dp_3"
            android:layout_marginStart="@dimen/dimen_dp_5" />

        <TextView
            android:id="@+id/tvDiscountDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_333"
            android:textSize="@dimen/dimen_dp_10"
            tools:text="满3包，24.5元/包；满4包，23元/包；满6包，22.5元/包满3包，24.5元/包；满4包，23元/包；满6包，22.5元/包" />
    </LinearLayout>

    <View
        android:id="@+id/divider1"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="#EEEEEE"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llDiscount" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupDiscount"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_0"
        android:visibility="gone"
        app:constraint_referenced_ids="llDiscount,divider1"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvBuyCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_21"
        android:text="购买数量"
        android:textColor="@color/color_333"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider1" />

    <include
        layout="@layout/layout_spell_group_sku_edit"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/tvBuyCount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvBuyCount" />

    <TextView
        android:id="@+id/tvTotal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_17"
        android:text="合计："
        android:textColor="@color/color_ff2121"
        android:textSize="@dimen/dimen_dp_19"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBuyCount" />

    <TextView
        android:id="@+id/tvTips"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_31"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="#FFF7CF"
        android:gravity="center"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTotal"
        tools:text="店铺5000元包邮，距包邮还差488.45元" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvSubmit"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTips"
        app:rv_backgroundColor="@color/color_00b377"
        app:rv_cornerRadius="@dimen/dimen_dp_2" />

</com.ybmmarket20.common.widget.RoundConstraintLayout>