<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8">

    <com.ybmmarket20.view.homesteady.HomeSteadyBannerView
        android:id="@+id/hsb_home_channel_banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_210"
        android:scaleType="centerCrop"
        app:AutoPlayTime="5000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pointContainerLeftRightPadding="@dimen/dimen_dp_2"
        app:pointNormal="@drawable/shape_banner_indicator_unselected"
        app:pointSelect="@drawable/shape_banner_indicator_selected" />

    <View
        android:id="@+id/v_header_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_91"
        android:layout_marginTop="@dimen/dimen_dp_200"
        android:background="@drawable/shape_home_channel_header_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybmmarket20.view.homesteady.HomeSteadyChannelHeaderNavigateView
        android:id="@+id/hschn_channel_navigate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_17"
        android:layout_marginStart="@dimen/dimen_dp_8"
        android:layout_marginEnd="@dimen/dimen_dp_8"
        android:background="@color/black"
        app:layout_constraintTop_toTopOf="@+id/v_header_bg" />

    <TextView
        android:id="@+id/tv_recommend_for_you"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/recommondforyou"
        android:textColor="@color/color_30303c"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/v_header_bg"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingTop="@dimen/dimen_dp_30"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_10"
        app:layout_constraintTop_toBottomOf="@+id/tv_recommend_for_you"
        android:background="@drawable/shape_home_steady_streamer_gradient" />


</androidx.constraintlayout.widget.ConstraintLayout>