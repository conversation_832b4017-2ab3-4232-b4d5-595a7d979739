<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:paddingBottom="@dimen/dimen_dp_5"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_70"
        android:layout_marginStart="@dimen/dimen_dp_38"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/shape_cart_goods_gift_item">

        <View
            android:id="@+id/bg"
            android:layout_width="@dimen/dimen_dp_60"
            android:layout_height="@dimen/dimen_dp_60"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:background="@drawable/shape_cart_goods_gift_image"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_cart_goods_gift"
            android:layout_width="@dimen/dimen_dp_56"
            android:layout_height="@dimen/dimen_dp_56"
            android:layout_marginStart="@dimen/dimen_dp_2"
            android:layout_marginTop="@dimen/dimen_dp_2"
            app:layout_constraintStart_toStartOf="@+id/bg"
            app:layout_constraintTop_toTopOf="@+id/bg" />

        <TextView
            android:id="@+id/tv_goods_gift_tag"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_15"
            android:background="@drawable/shape_goods_gift_tag"
            android:text="赠品"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_11"
            android:paddingStart="@dimen/dimen_dp_5"
            android:paddingEnd="@dimen/dimen_dp_5"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_goods_gift_title"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:layout_marginStart="@dimen/dimen_dp_6"
            android:layout_marginEnd="@dimen/dimen_dp_13"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="@color/color_292933"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/bg"
            app:layout_constraintTop_toTopOf="@+id/bg" />

        <TextView
            android:id="@+id/tv_goods_gift_effect"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_dp_13"
            android:layout_marginTop="@dimen/dimen_dp_4"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_11"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/bg"
            app:layout_constraintStart_toStartOf="@+id/tv_goods_gift_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_goods_gift_title" />

        <com.ybmmarket20.view.TagView
            android:id="@+id/tv_goods_gift_type_tag"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_13"
            android:layout_marginBottom="@dimen/dimen_dp_3"
            android:layout_marginStart="@dimen/dimen_dp_6"
            app:layout_constraintBottom_toBottomOf="@+id/bg"
            app:layout_constraintStart_toEndOf="@+id/bg"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/llZengPinPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_3"
            app:layout_constraintStart_toEndOf="@+id/tv_goods_gift_type_tag"
            app:layout_constraintBottom_toBottomOf="@+id/tv_goods_gift_type_tag"
            android:layout_marginEnd="@dimen/dimen_dp_18"
            android:layout_marginBottom="@dimen/dimen_dp_3"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvZengPinPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0.01元/支"
                android:textColor="@color/color_9494A6"
                android:textSize="@dimen/dimen_dp_11" />

            <ImageView
                android:id="@+id/ivZengPinTip"
                android:layout_width="@dimen/dimen_dp_12"
                android:layout_height="@dimen/dimen_dp_12"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dimen_dp_5"
                android:src="@drawable/icon_zengpin_tip" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_goods_gift_count"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_15"
            android:background="@drawable/shape_goods_gift_count"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_11"
            android:gravity="center"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:paddingStart="@dimen/dimen_dp_5"
            android:paddingEnd="@dimen/dimen_dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/bg"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="x20" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>