<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F7F7F7">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dimen_dp_10"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dimen_dp_6">

        <ImageView
            android:layout_width="@dimen/dimen_dp_92"
            android:layout_height="@dimen/dimen_dp_92"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:src="@drawable/icon_real_name_authentication_header"
            android:layout_gravity="center_horizontal" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_18"
            android:textColor="#333333"
            android:text="您的账号信息已经过实名认证"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_gravity="center_horizontal" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="信息仅用于身份验证，保障您的资金安全"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dimen_dp_8"
            android:layout_marginBottom="@dimen/dimen_dp_25"
            android:textColor="#676773" />
    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dimen_dp_6">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_44"
            android:text="身份信息"
            android:textSize="@dimen/dimen_dp_15"
            android:gravity="center_vertical"
            android:textStyle="bold"
            android:textColor="#292933" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#F5F5F5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_44"
            android:orientation="horizontal">

            <TextView
                android:layout_width="@dimen/dimen_dp_0"
                android:layout_height="@dimen/dimen_dp_44"
                android:layout_weight="1"
                android:text="姓名"
                android:textSize="@dimen/dimen_dp_13"
                android:gravity="center_vertical"
                android:textColor="#676773" />

            <TextView
                android:id="@+id/tvName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/dimen_dp_13"
                android:textColor="#292933"
                tools:text="张三" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0_5"
            android:background="#F5F5F5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_44"
            android:orientation="horizontal">

            <TextView
                android:layout_width="@dimen/dimen_dp_0"
                android:layout_height="@dimen/dimen_dp_44"
                android:layout_weight="1"
                android:text="身份证号"
                android:textSize="@dimen/dimen_dp_13"
                android:gravity="center_vertical"
                android:textColor="#676773" />

            <TextView
                android:id="@+id/tvIdCard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/dimen_dp_13"
                android:textColor="#292933"
                tools:text="4****************6" />
        </LinearLayout>

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <androidx.legacy.widget.Space
        android:id="@+id/space"
        android:layout_weight="1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_cancel_real_name_registration"
        android:layout_width="match_parent"
        android:gravity="center"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="10dp"
        android:text="清除实名认证"
        app:rv_backgroundColor="@color/color_00b955"
        android:textColor="@color/white"
        app:rv_cornerRadius="2dp"
        android:layout_height="44dp"/>

</LinearLayout>