package com.ybmmarket20.search

import com.analysys.ANSAutoPageTracker
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.adapter.SearchAdapter
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgReport
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.reportBean.PageListProductClick
import com.ybmmarket20.reportBean.PageListProductExposure
import com.ybmmarket20.utils.SpUtil

/**
 * 专区搜索
 */
@Router(
    "searchproduct",
    "searchproduct/:keyword",
    "searchproduct/:show",
    "searchproduct/:voice",
    "searchproduct/:id/:name",
    "searchproduct/:tagList",
    "searchproduct/:tagList/:title"
)
class SearchProductSectionActivity : SearchProductOPActivity(), ANSAutoPageTracker {

    override fun getCouponEntryType(): String =
        CouponEntryType.COUPON_ENTRY_TYPE_SEARCH_AREA_AND_SHOP

    override fun getAggsUrl(): String = AppNetConfig.SORTNET_AGGS_SECTION

    override fun getSearchUrl(): String = AppNetConfig.SORTNET_SECTION

    override fun getManufacturersUrl(): String = AppNetConfig.FIND_MANUFACTURER_SECTION

    override fun getSearchScene(): String = "9"

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackOldSearchIntermediateState.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackOldSearchIntermediateState.TITLE
        return properties
    }

    override fun registerPageUrl(): String  = this.getFullClassName()

    override fun getJgTrackBean(): JgTrackBean = JgTrackBean()

    override fun getParams(isLoadMore: Boolean): RequestParams {
        return super.getParams(isLoadMore).apply {
            putWithoutEncode("tags", tagList)
        }
    }

    override fun getAdapter(): YBMBaseListAdapter<*> {
        return super.getAdapter().apply {
            setSectionAdapterTrackData(
                (this as SearchAdapter).getListGoodsAdapter(),
                <EMAIL>()
            )
        }
    }

    override fun isSortnetSection(): Boolean {
        return true
    }
}