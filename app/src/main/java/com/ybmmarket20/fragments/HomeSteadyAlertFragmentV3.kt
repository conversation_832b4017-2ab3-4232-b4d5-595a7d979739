package com.ybmmarket20.fragments

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.viewModels
import com.ybm.app.bean.NetError
import com.ybmmarket20.activity.AptitudeActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.DialInfoBean
import com.ybmmarket20.bean.HomeAlertBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.responseDialogAnalysisClose
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.message.Message
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.DialImageView
import com.ybmmarket20.view.ShowBottomSheetDialog
import com.ybmmarket20.view.homesteady.callback.IHomeAlertAnalysisCallbackV3
import com.ybmmarket20.view.homesteady.callback.IHomeDialogCustomCallback
import com.ybmmarket20.viewmodel.HOME_ALERT_TYPE_CMS
import com.ybmmarket20.viewmodel.HOME_ALERT_TYPE_LICENSE
import com.ybmmarket20.viewmodel.HomeAlertViewModel
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarketkotlin.fragments.CustomCouponDialogFragment
import com.ybmmarketkotlin.fragments.CustomImageDialogFragment
import com.ybmmarketkotlin.fragments.DIALOG_TAG_CUSTOM_COUPON
import com.ybmmarketkotlin.fragments.DIALOG_TAG_CUSTOM_IMAGE
import com.ybmmarketkotlin.fragments.DialogFragmentManager
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v3.iv_close

/**
 * <AUTHOR>
 * @date 2020-05-12
 * @description  处理首页弹框 和 消息
 */

abstract class HomeSteadyAlertFragmentV3: HomeSteadyAnalysisFragmentV2(){

    private var tvBubble: TextView? = null
    // 首页弹窗可能会返回多个窗口，是一个序列，保证当前序列已经完成
    private var traceFinished: Boolean = true
    // 是否已经订阅首页弹窗数据
    private var isHomeAlertObserved = false
    private val homeAlertViewModel: HomeAlertViewModel by viewModels()
    protected var closeIvDialSuspension = false //是否关闭关大转盘，关过以后刷新数据也不会再展示
    protected var ivDialSuspensionNeedShow = false //大转盘是否可以展示且没有关闭

    /**
     * 请求首页弹窗
     */
    fun requestHomeAlert() {
        requestHomeAlert(null)
    }

    fun requestHomeAlert(requestType: String? = null) {
        if (!isHomeAlertObserved) observeHomeAlert()
        isHomeAlertObserved = true
        homeAlertViewModel.requestAlert(mutableMapOf(
            "type" to (requestType?: "0")
        ))
    }

    private fun observeHomeAlert() {
        homeAlertViewModel.homeAlertLiveData.observe(this) {
            val adShowing = (activity as? MainActivity)?.isAdShowing
            if( adShowing != true) {
                showAlert(it)
            }
        }
    }

    fun showAlert(mBean:BaseBean<HomeAlertBean>?){
        val bean = mBean?:homeAlertViewModel.homeAlertLiveData.value
        bean?:return
        if (bean.isSuccess) {
            val data = bean.data
//                if (data == null || data.finishFlag == 0) {
//                    traceFinished = true
//                }
            when(data.type) {
                //资质
                HOME_ALERT_TYPE_LICENSE -> HomeAlertState.HomeAlertLicenseState(data, activity)
                //CMS
                HOME_ALERT_TYPE_CMS -> HomeAlertState.HomeAlertCMSState(data, activity, this)
                //大转盘
//                    HOME_ALERT_TYPE_WHEEL -> HomeAlertState.HomeAlertWheelState(data.bigWheel, getDialSuspension())
                else -> null
            }?.handleAlert(data.type, ::requestHomeAlert)
        }
    }

    abstract fun getDialSuspension(): DialImageView

    sealed class HomeAlertState {

        abstract fun handleAlert(type: Int, callback: (String?)->Unit)

        /**
         * 资质
         */
        class HomeAlertLicenseState(val alertBean: HomeAlertBean?, val context: Context?): HomeAlertState() {
            override fun handleAlert(type: Int, callback: (String?)->Unit) {
                val bean = alertBean?.license
                context?.apply {
                    AlertDialogEx(this)
                        .setCanceledOnTouchOutside(false)
                        .setTitle(bean?.title)
                        .setMessage(bean?.msg)
                        .setCorner()
                        .setConfirmButton("去更新资质", "#00B377") {_, _ ->
//                            callback("$type")
                            (context as BaseActivity).gotoAtivity(AptitudeActivity::class.java, null)
                            getLicenseType(bean?.status?: 0)?.let {
                                XyyIoUtil.track("Homepage_Qualification_Popup_Click", hashMapOf(
                                    "type" to it
                                ))

                            }
                        }.show()
                    getLicenseType(bean?.status?: 0)?.let {
                        XyyIoUtil.track("Homepage_Qualification_Popup_Exposure", hashMapOf(
                            "type" to it
                        ))
                    }

                }
            }

            private fun getLicenseType(status: Int): String? {
                //1.临期&过期 2.仅过期 3.仅临期
                return if (status == 1) {
                    "expire_and_advent"
                } else if (status == 2) {
                    "expire"
                } else if (status == 3) {
                    "advent"
                } else {
                    null
                }
            }
        }

        /**
         * CMS
         */
        class HomeAlertCMSState(val alertBean: HomeAlertBean?, val activity: Activity?, val dialogCallback: IHomeDialogCustomCallback?): HomeAlertState() {
            override fun handleAlert(type: Int, callback: (String?)->Unit) {
                val bean = alertBean?.cms
                val style = bean?.style?: -1
                val tag = if (style < 70) {
                    DIALOG_TAG_CUSTOM_IMAGE
                } else if (style < 80) {
                    DIALOG_TAG_CUSTOM_COUPON
                } else {
                    null
                }
                if (tag == null) return
                val newDialogFragment: DialogFragment? = if (tag == DIALOG_TAG_CUSTOM_IMAGE && bean?.detail?.imageDtos != null && bean.detail.imageDtos.isNotEmpty()) {
                    dialogCallback?.onDialogCustomExposure(alertBean?.cms?.detail?.trackData)
                    setTrackDataSpmE(bean?.detail?.customImageTrackDataInfo?.trackData, alertBean?.cms?.detail?.trackData)
                    dialogCallback?.onDialogImageExposure(bean.detail.customImageTrackDataInfo?.trackData)
                    CustomImageDialogFragment.newInstanceWithData(alertBean, bean.detail.imageDtos, {
                        callback.invoke("$type")
                    }, dialogCallback)
                } else if (tag == DIALOG_TAG_CUSTOM_COUPON && bean?.detail?.couponDtos != null && bean.detail.couponDtos.isNotEmpty()) {
                    dialogCallback?.onDialogCustomExposure(alertBean?.cms?.detail?.trackData)
                    setTrackDataSpmE(bean?.detail?.customImageTrackDataInfo?.trackData, alertBean?.cms?.detail?.trackData)
                    setTrackDataSpmE(bean?.detail?.customCouponTrackData?.trackData, alertBean?.cms?.detail?.trackData)
                    if (bean?.detail?.imageDtos != null && bean.detail.imageDtos.isNotEmpty()) {
                        dialogCallback?.onDialogImageExposure(bean.detail.customImageTrackDataInfo?.trackData)
                    }
                    dialogCallback?.onDialogCouponExposure(bean.detail.customCouponTrackData?.trackData)
                    CustomCouponDialogFragment.newInstanceWithData(alertBean, bean.detail, {
                        callback.invoke("$type")
                    }, dialogCallback)
                } else null
                newDialogFragment?.let {
                    DialogFragmentManager.showDialogFragment(
                        it,
                        activity,
                        tag,
                        bean?.sceneType?: ""
                    )
                }
            }

            private fun setTrackDataSpmE(trackData: TrackData?, spmTrackData: TrackData?) {
                trackData?.spmEntity?.spmE = spmTrackData?.spmEntity?.spmE
            }

            private fun showDialogFragment(newDialogFragment: DialogFragment, activity: Activity?, callback: (String?)->Unit, type: Int, tag: String) {
                if (activity == null) return
                val ft: FragmentTransaction = (activity as FragmentActivity).supportFragmentManager.beginTransaction()
                val prevFragment = activity.supportFragmentManager.findFragmentByTag(tag)
                prevFragment?.let {
                    ft.remove(it)
                }
                newDialogFragment.show(ft, tag)
                newDialogFragment.dialog?.setOnDismissListener {
                    responseDialogAnalysisClose("99")
                    callback("$type")
                }
            }

        }

        /**
         * 大转盘
         */
        class HomeAlertWheelState(val bean: DialInfoBean?, private val ivDialSuspension: DialImageView): HomeAlertState() {
            override fun handleAlert(type: Int, callback: (String?)->Unit) {
                bean?.also {
                    if (!TextUtils.isEmpty(bean.appImageUrl)) {
                        ivDialSuspension.setItemData(it, type)
                        ivDialSuspension.visibility = View.VISIBLE
                    } else {
                        ivDialSuspension.visibility = View.GONE
                    }
                }
            }
        }
    }

    /**
     * 大转盘
     * fatherLayout父容器显隐藏  设置就显示隐藏父容器
     */
    fun getTurnTable(iv_dial_suspension: DialImageView,fatherLayout: ViewGroup? = null){
        val params = RequestParams.newBuilder()
            .url(AppNetConfig.HOME_DIAL_INFO)
            .addParam("merchantId", HttpManager.getInstance().merchant_id)
            .build()
        HttpManager.getInstance().post(params, object : BaseResponse<DialInfoBean>() {
            override fun onSuccess(content: String, obj: BaseBean<DialInfoBean>?, bean: DialInfoBean?) {
                if (closeIvDialSuspension){ //关闭 不显示
                    fatherLayout?.let {
                        it.isVisible = false
                        iv_dial_suspension.isVisible = false
                    }?: kotlin.run {
                        iv_dial_suspension.isVisible = false
                    }
                }else{
                    if (obj != null && obj.isSuccess) {
                        bean?.also {
                            if (!TextUtils.isEmpty(bean.appImageUrl)) {
                                iv_dial_suspension.visibility = View.VISIBLE
                                iv_close.visibility = View.VISIBLE
                                iv_dial_suspension.setHomeAlertAnalysisCallback(this@HomeSteadyAlertFragmentV3)
                                iv_dial_suspension.setIHomeBigWheelAnalysisCallback(object :IHomeAlertAnalysisCallbackV3{
                                    override fun onAlertExposureCallback(trackData: TrackData?) {
                                        <EMAIL>(trackData)
                                    }

                                    override fun onAlertClickCallback(trackData: TrackData?) {
                                        <EMAIL>(trackData)
                                    }
                                })
                                iv_dial_suspension.setItemData(it)
                                fatherLayout?.isVisible = true
                                ivDialSuspensionNeedShow = true
                                TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_FLOAT_EXPOSURE)
                            } else {
                                iv_dial_suspension.visibility = View.GONE
                                iv_close.visibility = View.GONE
                                fatherLayout?.isVisible = false
                                ivDialSuspensionNeedShow = false
                            }
                        }
                    } else {
                        iv_dial_suspension.visibility = View.GONE
                        iv_close.visibility = View.GONE
                        fatherLayout?.isVisible = false
                        ivDialSuspensionNeedShow = false
                    }
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
                iv_dial_suspension.visibility = View.GONE
                fatherLayout?.isVisible = false
                ivDialSuspensionNeedShow = false
            }
        })
    }

    /**
     * 处理首页消息数量
     */
    fun initMessageCountForBubble(tvBubbule: TextView?) {
        tvBubbule?.also {
            this.tvBubble = it
            Message.instance.bindUnreadMsgCount(this)
        }
    }

    /**
     * 拨打电话
     */
    private fun callPhone(phone: String) {
        showBottomSheetDialog(phone)
    }

    /**
     * 拨打电话
     */
    private fun showBottomSheetDialog(mobile: String) {
        if (TextUtils.isEmpty(mobile)) {
            ToastUtils.showShort("联系电话为空")
            return
        }
        ShowBottomSheetDialog(notNullActivity).also {
            it.initData(mobile, -1)
            it.show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Message.instance.releaseListener(this)
    }

    override fun countChange(count: Int) {
        if (count <= 0) {
            tvBubble?.visibility = View.GONE
        } else if (count in 1..9) {
            tvBubble?.visibility = View.VISIBLE
            tvBubble?.text = count.toString()
        } else {
            tvBubble?.visibility = View.VISIBLE
            tvBubble?.text = "9+"
        }
    }
}