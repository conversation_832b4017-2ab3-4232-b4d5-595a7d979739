package com.ybmmarket20.activity;

import android.text.TextUtils;

import com.apkfuns.logutils.LogUtils;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.YBMAppLike;
import com.ybm.app.utils.IntentUtil;
import com.ybmmarket20.home.MainActivity;

/**
 * 分发页面，页面为透明,为外面调用app使用，和应用在后台时，清理app内存时使用
 * 场景1，自己在后台马上要被kill,自己结束自己，然后调用这个activity
 * 场景2，自己在后台马上一段时间，应用内存不足，清理全部activity，然后调用这个activity,如果应该进入，后
 */
@Router({"dispatch", "dispatch/:scene"})
public class DispatchActivity extends BaseActivity {
    private String scene;
    protected String api;
    private boolean isFirst = false;
    private static final String SCENE_KILL = "scene_kill";
    @Override
    protected void initData() {
        if (getIntent() == null || YBMAppLike.getApp() == null) {
            try {
                BaseYBMApp.appBgTime = System.currentTimeMillis();
                gotoAtivity(MainActivity.class);
            } catch (Throwable e) {
                BugUtil.sendBug(e);
            }
            finish();
            return;
        }

        LogUtils.d("DispatchActivity 启动成功");
        try {
            scene = getIntent().getStringExtra("scene");
        } catch (Exception ignore) {
        }
        if (YBMAppLike.getApp().isForeground()) {
            LogUtils.d("执行返回home操作");
            IntentUtil.openHome(this);
        }
        isFirst = true;
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_voucher_disaptch;
    }

    @Override
    protected void onStop() {
        super.onStop();
        isFirst = false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isFirst) {
            isFirst = false;
        } else {
            if (SCENE_KILL.equals(scene) || TextUtils.isEmpty(scene)) {//跳转
                BaseYBMApp.appBgTime = System.currentTimeMillis();
                gotoAtivity(MainActivity.class);
                finish();
            }
        }
    }
}
