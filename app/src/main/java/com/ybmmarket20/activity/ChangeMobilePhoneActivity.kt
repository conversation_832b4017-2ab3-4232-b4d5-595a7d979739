package com.ybmmarket20.activity

import android.os.CountDownTimer
import androidx.core.content.ContextCompat
import android.text.Editable
import android.text.InputType
import android.text.TextUtils
import android.text.TextWatcher
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import com.ybm.app.bean.NetError
import com.ybm.app.common.BaseYBMApp
import com.ybm.app.utils.Utils
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.navigationbar.DefaultNavigationBar
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.TextColorChangeUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.TimeCountUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.XEditText
import kotlinx.android.synthetic.main.activity_change_mobile_phone.*

/**
 * @author: yuhaibo
 * @time: 2019-08-15 10:48.
 * projectName: ybm-android.
 * Description: 更换手机号码
 */
class ChangeMobilePhoneActivity : BaseActivity() {
    private var mPasswordTextWatcher: TextWatcher? = null
    private var mPhoneNumTextWatcher: TextWatcher? = null
    private var mAuthCodeTextWatcher: TextWatcher? = null
    private var isPasswordChange = false
    private var isPhoneNumChange = false
    private var isAuthCodeChange = false
    private var mCountDownTimer: CountDownTimer? = null
    private var mIsShow = false

    override fun getContentViewId(): Int = R.layout.activity_change_mobile_phone

    override fun initData() {
        initView()
        initTextWatcher()
    }

    private fun initView() {
        tv_submit.isEnabled = false
        val accountList = AccountTable.getAllAccount(Abase.getContext())
        val currentAccountBean = accountList?.find { SpUtil.getMerchantid() == it.merchantId }
        tv_current_phoneNum.text = "当前账户：${currentAccountBean?.phone ?: ""}"
        TextColorChangeUtils.setInterTextColor(this, tv_password_title, "", "*", "密码：", R.color.brand_icon_type3)
        TextColorChangeUtils.setInterTextColor(this, tv_newPhoneNum_title, "", "*", "认证手机号：", R.color.brand_icon_type3)
        TextColorChangeUtils.setInterTextColor(this, tv_verificationCode_title, "", "*", "验证码：", R.color.brand_icon_type3)
        et_input_password.setDrawableRightListener(object : XEditText.DrawableRightListener {
            override fun onDrawableRightClick() {
                if (mIsShow) {
                    et_input_password.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.open, 0)
                    et_input_password.inputType = InputType.TYPE_CLASS_TEXT or EditorInfo.TYPE_TEXT_VARIATION_PASSWORD
                } else {
                    et_input_password.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.pwd_clouse, 0)
                    et_input_password.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
                }
                mIsShow = !mIsShow
            }
        })
        //倒计时
        login_btn_authCode.setOnClickListener {
            val passwordStr = et_input_password?.text?.toString()
            val phoneNum = login_et_phoneNum?.text?.toString()

            if (TextUtils.isEmpty(phoneNum) || !UiUtils.isMobileNO(phoneNum)) {
                ToastUtils.showShort(R.string.validate_mobile_error)
                return@setOnClickListener
            }
            getSendCodeData(passwordStr, phoneNum)
            login_btn_authCode.isClickable = false
        }
        /**
         * 提交按钮点击事件
         */
        tv_submit.setOnClickListener {
            val phoneNumStr = login_et_phoneNum?.text?.toString()
            val passwordStr = et_input_password?.text?.toString()
            val verifyCode = login_et_verificationCode?.text?.toString()
            getSubmit(phoneNumStr, passwordStr, verifyCode)
        }
    }

    override fun initHead() {
        super.initHead()
        DefaultNavigationBar.Builder(this).setTitle("更换手机号").build()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mPasswordTextWatcher != null) {
            et_input_password.removeTextChangedListener(mPasswordTextWatcher)
        }
        if (mPhoneNumTextWatcher != null) {
            login_et_phoneNum.removeTextChangedListener(mPhoneNumTextWatcher)
        }
        if (mAuthCodeTextWatcher != null) {
            login_et_verificationCode.removeTextChangedListener(mAuthCodeTextWatcher)
        }
        mCountDownTimer?.cancel()
        mCountDownTimer = null
    }

    private fun initTextWatcher() {
        mPasswordTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                isPasswordChange = !TextUtils.isEmpty(s)
                tv_submit.isEnabled = isPasswordChange && isPhoneNumChange && isAuthCodeChange
            }
        }
        et_input_password.addTextChangedListener(mPasswordTextWatcher)
        mPhoneNumTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                isPhoneNumChange = !TextUtils.isEmpty(s)
                tv_submit.isEnabled = isPasswordChange && isPhoneNumChange && isAuthCodeChange
            }
        }
        login_et_phoneNum.addTextChangedListener(mPhoneNumTextWatcher)
        mAuthCodeTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                isAuthCodeChange = !TextUtils.isEmpty(s)
                tv_submit.isEnabled = isPasswordChange && isPhoneNumChange && isAuthCodeChange
            }
        }
        login_et_verificationCode.addTextChangedListener(mAuthCodeTextWatcher)
    }

    /**
     * 发送验证码
     */
    private fun getSendCodeData(passwordStr: String?, phoneNum: String?) {
        val params = RequestParams
                .newBuilder()
                .url(AppNetConfig.SEND_CODE)
                .addParam("mobile", phoneNum)
                .addParam("password", passwordStr)
                .addParam("changeFlag", "1")
                .addParam("merchantId", SpUtil.getMerchantid())
                .addParam("version", Utils.getVersionName(BaseYBMApp.getAppContext()))
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<Any>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<Any>?, t: Any?) {
                super.onSuccess(content, baseBean, t)
                if (baseBean == null) return
                if (baseBean.isSuccess) {
                    mCountDownTimer = TimeCountUtil(login_btn_authCode, 120000, 1000)
                            .setCountdownListener(object : TimeCountUtil.CountdownListener {
                                override fun onCountdownIngListener(view: TextView?, secondsNum: String) {
                                    view?.text = "${secondsNum}s重新获取"
                                    view?.setTextColor(ContextCompat.getColor(this@ChangeMobilePhoneActivity, R.color.color_9494A6))
                                    login_btn_authCode.setStrokeColor(ContextCompat.getColor(this@ChangeMobilePhoneActivity, R.color.color_9494A6))
                                }

                                override fun onCountdownFinishListener(view: TextView?) {
                                    view?.setTextColor(ContextCompat.getColor(this@ChangeMobilePhoneActivity, R.color.color_00B377))
                                    login_btn_authCode.setStrokeColor(ContextCompat.getColor(this@ChangeMobilePhoneActivity, R.color.color_00B377))
                                }
                            }).start()
                }
                ToastUtils.showShort(baseBean.msg)
                login_btn_authCode.isClickable = true
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                ToastUtils.showShort(error?.message)
                login_btn_authCode.isClickable = true
            }
        })
    }

    /**
     * 提交
     */
    private fun getSubmit(phoneNumStr: String?, passwordStr: String?, verifyCode: String?) {
        val params = RequestParams
                .newBuilder()
                .url(AppNetConfig.CHANGE_PHONE_SUBMIT)
                .addParam("authMobile", phoneNumStr)
                .addParam("password", passwordStr)
                .addParam("changeFlag", "1")
                .addParam("loginType", "2")
                .addParam("verificationCode", verifyCode)
                .addParam("ip", SpUtil.getDeviceId())
                .addParam("merchantId", SpUtil.getMerchantid()).build()
        HttpManager.getInstance().post(params, object : BaseResponse<Any>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<Any>?, t: Any?) {
                super.onSuccess(content, baseBean, t)
                if (baseBean == null) return
                if (baseBean.isSuccess) {
                    ToastUtils.showShort("修改成功")
                    finish()
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                ToastUtils.showShort("提交失败")
            }
        })

    }
}
