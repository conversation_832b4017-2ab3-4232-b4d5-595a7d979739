package com.ybmmarket20.activity.afterSales.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.fragment.RefundOrAfterSalesFragment
import com.ybmmarket20.bean.aftersales.RefundOrAfterSalesEvent
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.eventbus.Event
import kotlinx.android.synthetic.main.activity_refund_or_sales.etSearch
import kotlinx.android.synthetic.main.activity_refund_or_sales.ivEditClose
import kotlinx.android.synthetic.main.activity_refund_or_sales.rtvBubble
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvFinish
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvFinishIndicator
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvProcessing
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvProcessingIndicator

/**
 * 退款/售后
 */

//退款/售后状态-进行中
const val REFUND_OR_AFTER_SALES_STATUS_PROCESSING = 0
//退款/售后状态-已完成
const val REFUND_OR_AFTER_SALES_STATUS_FINISH = 1

@Router("refundoraftersales")
class RefundOrAfterSalesActivity: BaseActivity() {

    private var mOrderNo: String? = null
    private var mKeyWord: String? = null
    private var mCurrentType: Int = REFUND_OR_AFTER_SALES_STATUS_PROCESSING
    private var processingFragment: RefundOrAfterSalesFragment? = null
    private var finishFragment: RefundOrAfterSalesFragment? = null
    private var mCurrentFragment: RefundOrAfterSalesFragment? = null
    private var isReceiveNotificationForRefreshData = false

    override fun getContentViewId(): Int = R.layout.activity_refund_or_sales

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        mOrderNo = intent?.getStringExtra("orderNo")
        etSearch.setText(mOrderNo)
        etSearch.setSelection(mOrderNo?.length?: 0)
//        processingFragment?.refreshData()
//        finishFragment?.refreshData()
        if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_PROCESSING && mKeyWord == mOrderNo) return
        mKeyWord = mOrderNo?: ""
        mCurrentType = REFUND_OR_AFTER_SALES_STATUS_PROCESSING
        setTabState(mCurrentType, tvProcessing, tvFinish)
    }

    override fun initData() {
        setTitle("退款/售后")
        mOrderNo = intent.getStringExtra("orderNo")
        mKeyWord = mOrderNo?: ""
        switchFragment()
        tvProcessing.setOnClickListener {
            if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_PROCESSING) return@setOnClickListener
            mCurrentType = REFUND_OR_AFTER_SALES_STATUS_PROCESSING
            setTabState(mCurrentType, tvProcessing, tvFinish)
        }

        tvFinish.setOnClickListener {
            if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_FINISH) return@setOnClickListener
            mCurrentType = REFUND_OR_AFTER_SALES_STATUS_FINISH
            setTabState(mCurrentType, tvFinish, tvProcessing)
        }
        etSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                mKeyWord = etSearch.text.toString()
                searchList(mKeyWord)
                hideSoftInput()
            }
            return@setOnEditorActionListener true
        }
        etSearch.addTextChangedListener {
            if (it.isNullOrEmpty()) {
                View.GONE
            } else {
                View.VISIBLE
            }.let(ivEditClose::setVisibility)
        }
        etSearch.setText(mOrderNo)
        etSearch.setSelection(mOrderNo?.length?: 0)
        ivEditClose.setOnClickListener {
            mKeyWord = ""
            etSearch.setText("")
            searchList(mKeyWord)
        }
    }

    /**
     * 设置tab状态
     */
    private fun setTabState(status: Int, selectedView: TextView, unSelectedView: TextView) {
        selectedView.setTextColor(Color.parseColor("#292933"))
        selectedView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 17f)
        unSelectedView.setTextColor(Color.parseColor("#676773"))
        unSelectedView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15f)
        tvProcessingIndicator.visibility = if (status == REFUND_OR_AFTER_SALES_STATUS_PROCESSING) View.VISIBLE else View.GONE
        tvFinishIndicator.visibility = if (status == REFUND_OR_AFTER_SALES_STATUS_FINISH) View.VISIBLE else View.GONE
        switchFragment()
    }

    private fun switchFragment() {
        if (mCurrentFragment != null) {
            supportFragmentManager.beginTransaction().hide(mCurrentFragment!!).commitNowAllowingStateLoss()
        }
        val arguments = Bundle()
//        arguments.putString("orderNo", mOrderNo)
        arguments.putString("keyWord", mKeyWord)
        arguments.putString("status", if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_PROCESSING) "0" else "1")
        if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_PROCESSING) {
            if (processingFragment == null) {
                processingFragment = RefundOrAfterSalesFragment(mCurrentType)
                processingFragment!!.setItemCountCallback {
                    rtvBubble.text = "$it"
                    rtvBubble.visibility = if (it > 0) View.VISIBLE else View.GONE
                }
                processingFragment!!.arguments = arguments
                supportFragmentManager.beginTransaction().add(R.id.flReplace, processingFragment!!).commitNowAllowingStateLoss()
                mCurrentFragment = processingFragment
            } else {
                supportFragmentManager.beginTransaction().show(processingFragment!!).commitNowAllowingStateLoss()
                mCurrentFragment = processingFragment
                searchList(mKeyWord)
            }
        } else {
            if (finishFragment == null) {
                finishFragment = RefundOrAfterSalesFragment(mCurrentType)
                finishFragment!!.arguments = arguments
                supportFragmentManager.beginTransaction().add(R.id.flReplace, finishFragment!!).commitNowAllowingStateLoss()
                mCurrentFragment = finishFragment
            } else {
                supportFragmentManager.beginTransaction().show(finishFragment!!).commitNowAllowingStateLoss()
                mCurrentFragment = finishFragment
                searchList(mKeyWord)
            }
        }
    }

    /**
     * 搜索列表
     */
    private fun searchList(mKeyWord: String?) {
        if (mKeyWord != mCurrentFragment?.getKeyWord()) {
            mCurrentFragment?.search(mKeyWord?: "")
        }
        if (mCurrentType == REFUND_OR_AFTER_SALES_STATUS_FINISH) {
            //已完成搜索词更改保证处理中的气泡数量正确
            if (mKeyWord != processingFragment?.getKeyWord()) {
                processingFragment?.search(mKeyWord?: "")
            }
        }
    }


    override fun isRegisterEventBus(): Boolean = true

    override fun receiveEvent(event: Event<*>?) {
        super.receiveEvent(event)
        if (event?.data is RefundOrAfterSalesEvent) {
            isReceiveNotificationForRefreshData = true
        }
    }

    override fun onResume() {
        super.onResume()
        if (isReceiveNotificationForRefreshData) {
            isReceiveNotificationForRefreshData = false
            processingFragment?.refreshData()
            finishFragment?.refreshData()
        }
    }
}