package com.ybmmarket20.activity

import android.content.Intent
import android.os.Bundle
import androidx.viewpager.widget.ViewPager
import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.AgentOrderPageAdapter
import com.ybmmarket20.bean.AuthorizationDotCount
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.*
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.fragments.AgentOrderListFragment
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.IntervalListener
import com.ybmmarket20.utils.IntervalUtil
import com.ybmmarket20.utils.SpUtil
import kotlinx.android.synthetic.main.activity_agent_order.*

/**
 * 代下单专区
 * <AUTHOR>
 * 2019-10-14
 */

//全部
const val AGENT_ORDER_STATUS_ALL = -1
//待确认
const val AGENT_ORDER_STATUS_UNCONFIRM = 1
//已取消
const val AGENT_ORDER_STATUS_CANCELED = 2
//已驳回
const val AGENT_ORDER_STATUS_REJECTED = 3
@Router("agentorderactivity")
class AgentOrderActivity: BaseActivity() {

    var mCurrentPosition = 0
    private val agentOrderIntervalListener = AgentOrderIntervalListener()
    val fragments = ArrayList<AgentOrderListFragment>()

    override fun getContentViewId(): Int = R.layout.activity_agent_order

    override fun onResume() {
        super.onResume()
        getUnAuthorizationedCount()
    }

    override fun initData() {
        fragments.apply {
            add(createFragments(AGENT_ORDER_STATUS_ALL))
            add(createFragments(AGENT_ORDER_STATUS_UNCONFIRM))
            add(createFragments(AGENT_ORDER_STATUS_REJECTED))
            add(createFragments(AGENT_ORDER_STATUS_CANCELED))
        }
        registerInterval()
        val pageAdapter = AgentOrderPageAdapter(supportFragmentManager, fragments)
        nvp_agent_order_list.offscreenPageLimit = 3
        nvp_agent_order_list.adapter = pageAdapter
        nvp_agent_order_list.setScroll(false)
        stl_agent_order_list.setViewPager(nvp_agent_order_list)
        stl_agent_order_list.setIndicatorWidthEqualTitleHalf(true)
        iv_agent_order_search.setOnClickListener {
            startActivity(Intent(this, AgentOrderSearchActivity::class.java).apply {
                putExtra(AGENT_ORDER_ARGUMENT_KEY, "${getCurrentStatus(mCurrentPosition)}")
            })
        }
        tv_authorization.setOnClickListener {
            gotoAtivity(AuthorizationAreaActivity::class.java)
            finish()
        }
        nvp_agent_order_list.addOnPageChangeListener(object: ViewPager.SimpleOnPageChangeListener(){
            override fun onPageSelected(position: Int) {
                super.onPageScrollStateChanged(position)
                mCurrentPosition = position
            }
        })
    }

    /**
     * 获取当前tab选中的状态
     */
    private fun getCurrentStatus(position: Int):Int = when(position) {
        0 -> AGENT_ORDER_STATUS_ALL
        1 -> AGENT_ORDER_STATUS_UNCONFIRM
        2 -> AGENT_ORDER_STATUS_REJECTED
        else -> AGENT_ORDER_STATUS_CANCELED

    }

    /**
     * 创建Fragment集合
     */
    private fun createFragments(agentOrderStatus: Int): AgentOrderListFragment {
        return AgentOrderListFragment().apply {
            val bundle = Bundle()
            bundle.putInt(AGENT_ORDER_ARGUMENT_KEY, agentOrderStatus)
            arguments = bundle
        }
    }

    /**
     * 获取代下单专区红点数量
     */
    private fun getUnAuthorizationedCount() {
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        HttpManager.getInstance().post(AppNetConfig.AGENT_ORDER_AUTHORIZATION_DOT_COUNT, params, object : BaseResponse<AuthorizationDotCount>() {

            override fun onSuccess(content: String, obj: BaseBean<AuthorizationDotCount>, authorizationDotCount: AuthorizationDotCount) {
                super.onSuccess(content, obj, authorizationDotCount)
                if (authorizationDotCount.authorizeCount == 0) {
                    tv_agent_order_dot.visibility = View.GONE
                } else {
                    tv_agent_order_dot.visibility = View.VISIBLE
                    tv_agent_order_dot.text = authorizationDotCount.authorizeCount.let {
                        if(it > 9) "9+" else it.toString()
                    }.toString()
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        //取消注册
        IntervalUtil.unRegisterInterval(agentOrderIntervalListener)
    }

    /**
     * 注册时间监听
     */
    fun registerInterval() {
        IntervalUtil.registerInterval(agentOrderIntervalListener)
    }

    /**
     * 监听全部和待确认标签的待确认订单
     */
    inner class AgentOrderIntervalListener: IntervalListener {
        override fun callback() {
            fragments.filter {
                it.getAgentOrderTabStatus() == AGENT_ORDER_STATUS_ALL
                        || it.getAgentOrderTabStatus() == AGENT_ORDER_STATUS_UNCONFIRM
            }.forEach{
                it.updateUnConfirmOrderTime()
            }
        }
    }
}
//参数KEY
const val AGENT_ORDER_ARGUMENT_KEY = "status"

