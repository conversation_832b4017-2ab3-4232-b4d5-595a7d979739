package com.ybmmarket20.business.order.ui;


import static com.ybmmarket20.bean.CheckOrderRowsBean.ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS;
import static com.ybmmarket20.bean.CheckOrderRowsBean.ITEM_TYPE_ORDER_LIST_DOUBLE_EXCEPTION;
import static com.ybmmarket20.bean.CheckOrderRowsBean.ITEM_TYPE_ORDER_LIST_NO_EXCEPTION;
import static com.ybmmarket20.bean.CheckOrderRowsBean.ITEM_TYPE_ORDER_LIST_SINGLE_EXCEPTION;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.jeremyliao.liveeventbus.LiveEventBus;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.common.LazyFragment;
import com.ybmmarket20.common.LiveEventBusManager;
import com.ybmmarket20.common.ordertopmanager.OrderTopManageableDialog;
import com.ybmmarket20.common.ordertopmanager.OrderTopOnDismissListener;
import com.ybmmarket20.common.ordertopmanager.OrderTopOnShowListener;
import com.ybmmarket20.common.ordertopmanager.OrderTopParam;
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport;
import com.ybmmarket20.xyyreport.page.orderList.OrderListReportUtil;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.RecyclerViewSwipe.RecyclerTouchListener;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.OrderDetailActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderListBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.bean.CheckOrderSearchRowsBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.OrderActionBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.OnResult;
import com.ybmmarket20.common.ReceiveMoneyAccountInfoDialog;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.OrderActionLayout;
import com.ybmmarket20.view.OrderItemAptitudeView;
import com.ybmmarket20.viewmodel.AccountInfoViewModel;
import com.ybmmarketkotlin.utils.AptitudeTipsUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;


public class OrderListFragment extends OrderListAnalysisFragment implements OnResult<OrderActionBean> {

    @Bind(R.id.refreshLayout)
    SmartRefreshLayout refreshLayout;
    @Bind(R.id.rv)
    RecyclerView rv;

    @Bind(R.id.layout_aptitude_tip)
    ConstraintLayout layoutAptitudeTip;
    @Bind(R.id.tv_aptitude_tip)
    TextView tvAptitudeTip;
    @Bind(R.id.tv_tip_order_list)
    TextView tvTipOrderList;

    private String title;

    private String orderState;
    private SimpleDateFormat dateFormat;
    private YBMBaseAdapter orderAdapter;
    private List<CheckOrderRowsBean> rows = new ArrayList<>();
    private int page = 0;
    private int bottom = com.ybm.app.utils.UiUtils.dp2px(6);
    protected RecyclerTouchListener onTouchListener;
    private boolean isFirst;
    private int currPosition;
    private String merchantid;

    private AccountInfoViewModel accountInfoViewModel;
    private SparseArray<String> traceProductData = new SparseArray<>();

    //是否需要隐藏资质
    private  boolean isNeedHideAptitude = false;

    public static OrderListFragment getInstance(int type, String title) {
        OrderListReportUtil.initOrderListReportInfo(type, title);
        OrderListFragment fragment = new OrderListFragment();
        fragment.title = title;
        Bundle bundle = new Bundle();
        bundle.putString(IntentCanst.ORDER_STATE, type + "");
        fragment.setArguments(bundle);
        return fragment;
    }

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (IntentCanst.ACTION_ORDER_LIST_REFRESH.equals(intent.getAction())) {
                getMyOrder(0);
                return;
            }
            if (IntentCanst.ACTION_ORDER_LIST_ITEM_REFRESH.equals(intent.getAction())) {
                OrderActionBean orderActionBean = intent.getParcelableExtra("bean");
//                刷新指定条目
                onRefresh(orderActionBean);
            }

        }
    };

    @Override
    protected void initData(String content){
    }

    @Override
    public void loadData() {
        Bundle arguments = getArguments();
        orderState = arguments.getString(IntentCanst.ORDER_STATE);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_ORDER_LIST_REFRESH);
        intentFilter.addAction(IntentCanst.ACTION_ORDER_LIST_ITEM_REFRESH);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(broadcastReceiver, intentFilter);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        accountInfoViewModel = new ViewModelProvider(this).get(AccountInfoViewModel.class);
        refreshLayout.setOnRefreshListener(refreshLayout ->
        {
            try {
                pvTrack();
            } catch (Exception e) {
                e.printStackTrace();
            }
            page = 0;
            getMyOrder(0);
        });
//        refreshLayout.autoRefresh();
        showProgress();
        getMyOrder(0);
        accountInfoViewModel.getLiveData().observe(getViewLifecycleOwner(), receiveMoneyAccountInfo -> {
            dismissProgress();
            new ReceiveMoneyAccountInfoDialog(getContext())
                    .setData(receiveMoneyAccountInfo)
                    .show();
        });
        orderAdapter = new OrderListAdapter(rows);
        orderAdapter.setOnLoadMoreListener(() -> getMyOrder(page), rv);
        orderAdapter.setEmptyView(getContext(), R.layout.layout_empty_view, R.drawable.icon_empty, "暂无订单");
        orderAdapter.openLoadMore(10, true);
        rv.setAdapter(orderAdapter);
        rv.setLayoutManager(new WrapLinearLayoutManager(getContext()));

        if ("0".equalsIgnoreCase(orderState)) {
            // 增加资质过期提示
            if (SpUtil.getValidityStatus() == 1 || SpUtil.getValidityStatus() == 2) {

                OrderTopParam orderTopParam = new OrderTopParam.Builder().setDialog(new OrderTopManageableDialog() {

                    @Override
                    public void setOnShowListener(@NonNull OrderTopOnShowListener listener) {

                    }

                    @Override
                    public void setOnDismissListener(@NonNull OrderTopOnDismissListener listener) {

                    }

                    @Override
                    public void show() {
                        if (layoutAptitudeTip != null){
                            layoutAptitudeTip.post(() -> {
                                layoutAptitudeTip.setVisibility(View.VISIBLE);
                            });
                        }

                    }

                    @Override
                    public void dismiss(boolean isPushBack) {
                        if (layoutAptitudeTip != null){
                            layoutAptitudeTip.post(() -> {
                                layoutAptitudeTip.setVisibility(View.GONE);
                            });
                        }
                    }

                    @Override
                    public boolean isCanShow() {
                        return true;
                    }
                }).setPriority(0).build();

                AptitudeTipsUtils.Companion.initAptitudeOverdueTip(getNotNullActivity(), layoutAptitudeTip, tvAptitudeTip, XyyIoUtil.PAGE_ORDERLIST,orderTopParam);
            }

            // 增加资质首营资质下载提示
//            tvTipOrderList.setVisibility(View.VISIBLE);
        }
        rv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });


        //增加滑动删除功能
        onTouchListener = new RecyclerTouchListener(getActivity(), rv);
        onTouchListener.setSwipeOptionViews(R.id.bg)//设置滑出的菜单
                .setIndependentViews(R.id.bt_1, R.id.bt_2, R.id.bt_3, R.id.bt_4, R.id.bt_5, R.id.bt_6, R.id.tv_refund,R.id.tv_more,R.id.tv_title,R.id.tv_shop_title,R.id.btn_check_receive_money_account, R.id.tvSystemCheck, R.id.tvMerchantCheck, R.id.rllUpdate, R.id.aptitudeView)
                .setSwipeable(R.id.fg, R.id.bg, new RecyclerTouchListener.OnSwipeOptionsClickListener() {
                    @Override
                    public void onSwipeOptionClicked(int viewID, int position) {
                        if (viewID == R.id.bg) {
                            //这里需要注意 添加header会导致position的位置会从header=0开始算起
                            int headerCount = orderAdapter.getHeaderLayoutCount();
                            showDialogDeleteOrder(position - headerCount);
                        }
                    }
                })
                .setItemClickable(new RecyclerTouchListener.OnRowClickListener() {//条目点击事件

                    @Override
                    public void onRowClicked(int position) {
                        int headerCount = orderAdapter.getHeaderLayoutCount();
                        if (position - headerCount < rows.size()) {
                            currPosition = position;
                            //这里需要注意 添加header会导致position的位置会从header=0开始算起
                            Intent intent = new Intent(getActivity(), OrderDetailActivity.class);
                            intent.putExtra(IntentCanst.ORDER_ID, rows.get(position - headerCount).id + "");
                            intent.putExtra(IntentCanst.ORDER_NO, rows.get(position - headerCount).orderNo + "");
                            startActivity(intent);
                        }
                    }

                    @Override
                    public void onIndependentViewClicked(int independentViewID, final int position) {//设置条目内点击事件


                    }
                });
        rv.addOnItemTouchListener(onTouchListener);
    }

    private void handleSystemAndMerchantException(Context context, String content, String orderNo) {
        new AlertDialogEx(context)
                .setMessage(content)
                .setMessageGravity(Gravity.START)
                .setTitle("资质异常提醒")
                .setCorner()
                .setCancelButton("稍后更新", "#9494A5", (dialog, button) -> {})
                .setConfirmButton("去更新", "#00B377", (dialog, button) -> {
                    RoutersUtils.open("ybmpage://aptitude");
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("order_no", orderNo);
                    trackParams.put("page_source", "reminder_popup");
                    XyyIoUtil.track("Update_Qualification", trackParams);
                })
                .show();
    }

    @Override
    protected void initTitle() {


    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_order_list;
    }

    public String getTitle() {
        return title;
    }

    public void getMyOrder(final int page) {
        getMyOrder(page, 10);
    }

    public void getMyOrder(final int page, final int pageSize) {
        if (rv == null) {
            return;
        }
        merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", pageSize + "");
        params.put("offset", String.valueOf(page));

        if ("101".equals(orderState)) {
            params.put("appraiseStatus", "1");
        } else {
            params.put("status", orderState);
        }
        HttpManager.getInstance().post(AppNetConfig.ORDER, params, new BaseResponse<CheckOrderListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderListBean> obj, CheckOrderListBean orderList) {
                dismissProgress();
                if (rv == null) {
                    return;
                }
                if (page == 0) {
                    refreshLayout.finishRefresh();
                }
                if (obj != null) {
                    if (obj.isSuccess() && orderList != null) {
                        if (page == 0) {
                            rows.clear();
                        }
                        for (CheckOrderRowsBean item : orderList.getRows()) {
                            item.localtime = SystemClock.elapsedRealtime();
                            item.responseLocalTime = System.currentTimeMillis();
                        }
                        rows.addAll(orderList.getRows());

                        int orderSize = orderList.getRows().size();
                        //添加滑动到底部的提示
                        if (orderSize < pageSize && !rows.isEmpty()) {
                            CheckOrderSearchRowsBean lastRowsBean = new CheckOrderSearchRowsBean();
                            lastRowsBean.setItemType(ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS);
                            rows.add(lastRowsBean);
                        }
                        orderAdapter.notifyDataChangedAfterLoadMore(page == 0, orderSize >= pageSize);

                        if (orderList.getRows().size() >= pageSize) {
                            OrderListFragment.this.page++;
                        }
                    } else {
                        orderAdapter.setNewData(rows);
                    }

                    //更新待支付订单数的气泡
                    if (orderState.equals("10")){
                        LiveEventBus.get(LiveEventBusManager.OrderBus.NO_PAY_ORDERS_BUBBLE,Boolean.class).post(true);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if (rv == null) {
                    return;
                }
                if (page == 0) {
                    refreshLayout.finishRefresh();
                }
                orderAdapter.setNewData(rows);
            }
        });
    }

    //  网络请求删除服务器数据
    private void showDialogDeleteOrder(final int position) {

        final AlertDialogEx alert = new AlertDialogEx(getContext());
        alert.setTitle("删除");
        alert.setMessage("您确认删除吗？");
        alert.setCancelButton("取消", null);
        alert.setConfirmButton("确定", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                showProgress();
                if (rows == null || rows.isEmpty() || rows.size() <= position) {
                    dismissProgress();
                    return;
                }
                CheckOrderRowsBean rowsBean = rows.get(position);
                String merchantId = SpUtil.getMerchantid();
                RequestParams params = new RequestParams();
                params.put("merchantId", merchantId);
                params.put("id", String.valueOf(rowsBean.id));
                HttpManager.getInstance().post(AppNetConfig.ORDERS_DELETE, params, new BaseResponse<EmptyBean>() {

                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {
                        dismissProgress();
                        if (data != null) {
                            if (data.isSuccess()) {
                                rows.remove(position);
                                orderAdapter.notifyDataSetChanged();
                            }
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                    }
                });
            }
        });
        alert.show();
    }

    @Override
    public void onResult(boolean succ, OrderActionBean orderActionBean) {
        if (succ) {


        }

    }
    @Override
    public void onRefresh(OrderActionBean bean) {

        if (bean == null && orderAdapter == null) {
            return;
        }
        List<CheckOrderRowsBean> data = orderAdapter.getData();
        if (data == null || data.size() == 0) {
            return;
        }

        int index = -1;
        for (int i = 0; i < data.size(); i++) {
            CheckOrderRowsBean checkOrderRowsBean = data.get(i);
            if ((checkOrderRowsBean.id + "").equals(bean.id)) {
                index = i;
                checkOrderRowsBean.balanceStatus = bean.balanceStatus;
                checkOrderRowsBean.canConfirmReceipt = bean.canConfirmReceipt;
                break;
            }
        }

        if (index != -1) {
            orderAdapter.notifyItemChanged(index);
        }

        //更新待支付订单数的气泡
        if (orderState.equals("10")){
            LiveEventBus.get(LiveEventBusManager.OrderBus.NO_PAY_ORDERS_BUBBLE,Boolean.class).post(true);
        }

    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(broadcastReceiver);

    }

    class OrderListAdapter extends YBMBaseMultiItemAdapter<CheckOrderRowsBean> {

        public OrderListAdapter(List<CheckOrderRowsBean> data) {
            super(data);
            addItemType(ITEM_TYPE_ORDER_LIST_NO_EXCEPTION, R.layout.order_list_item_new);
            addItemType(ITEM_TYPE_ORDER_LIST_DOUBLE_EXCEPTION, R.layout.order_list_item_new);
            addItemType(ITEM_TYPE_ORDER_LIST_SINGLE_EXCEPTION, R.layout.order_list_item_new);
            addItemType(ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS, R.layout.item_order_search_last);
        }


        @Override
        protected void bindItemView(YBMBaseHolder baseViewHolder, CheckOrderRowsBean bean) {
            //最后一个
            if (bean.getItemType() == ITEM_TYPE_ORDER_LIST_BOTTOM_TIPS) return;

            String status1 = "审核中";
            String status7 = "出库中";
            String status2 = "配送中";
            String status3 = "已完成";
            String status4 = "已取消";
            String status6 = "已拆单";
            String status10 = "待支付";
            String status21 = "已拒签";
            String status20 = "已送达";
            String status90 = "退款审核中";
            String status91 = "已退款";

            String format = dateFormat.format(new Date(bean.createTime));
            baseViewHolder.setText(R.id.tv_create_time, "下单时间：" + format);
            TextView status = baseViewHolder.getView(R.id.tv_order_status);
            OrderItemAptitudeView aptitudeView = baseViewHolder.getView(R.id.aptitudeView);
            status.setVisibility(View.VISIBLE);
            switch (bean.status) {
                case 1:
                    status.setText(status1);
                    break;
                case 7://action 再次购买，申请退款（只有详情有）
                    status.setText(status7);
                    break;
                case 2://要有是否收到货的选择对话框
                    status.setText(status2);
                    break;
                case 3://申请退货(详情有),确认收货，再次购买
                    status.setText(status3);
                    break;
                case 4://再次购买
                    status.setText(status4);
                    break;
                case 6:
                    status.setText(status6);
                    break;
                case 10:// 取消订单，去支付
                        /*iv.setImageDrawable(getResources().getDrawable(R.drawable.order_status_10));
                        status.setText(status10);*/
//                        Log.e("xyd", "posion = " + baseViewHolder.getBindingAdapterPosition() + "item hashcode = " + baseViewHolder.hashCode() + "; textview hashcode = " + tvUnpayTimeCountdown.hashCode());
//                    if (bean.showExpireReminder && bean.countDownNewTime > 0) {
//                        status.setVisibility(View.GONE);
//                        llUnpayLayout.setVisibility(View.VISIBLE);
//                        long currentRealTime = SystemClock.elapsedRealtime();
//                        long realCountdownTime = bean.countDownNewTime * 1000 - currentRealTime + bean.localtime;
//                        TextViewKt.addCountDown(tvUnpayTimeCountdown, realCountdownTime, null, null, () -> {
//                            getMyOrder(0);
//                            return null;
//                        });
//                    } else {
//                        llUnpayLayout.setVisibility(View.GONE);
//                        status.setVisibility(View.VISIBLE);
//                        status.setText(status10);
//                    }
                    status.setText(status10);
                    break;
                case 21://再次购买，申请退款（只有详情有）
                    status.setText(status21);
                    break;
                case 20://再次购买，确认收货，申请退货（部分，只有详情有）
                    status.setText(status20);
                    break;
                case 90:
                    status.setText(status90);
                    break;
                case 91:
                    status.setText(status91);
                    break;
            }

            baseViewHolder.setText(R.id.tv_order_number, "共"+bean.varietyNum + "种");
            SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
            showPriceStr.append("¥").append(UiUtils.transform(bean.money));
            showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), 1, showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            showPriceStr.setSpan(new AbsoluteSizeSpan(14, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            baseViewHolder.setText(R.id.tv_price, showPriceStr);
            Group groupOne = baseViewHolder.getView(R.id.group_one);
            RecyclerView rv = baseViewHolder.getView(R.id.rv_goods);
            rv.setAdapter(null);
            if (bean.orderImages != null && bean.orderImages.size()>0){
                if (bean.orderImages.size() > 1){
                    groupOne.setVisibility(View.INVISIBLE);
                    rv.setVisibility(View.VISIBLE);
                    ArrayList<CheckOrderRowsBean.OrderImages> mDataList = new ArrayList<>();
                    if (bean.orderImages != null){
                        if (bean.orderImages.size() > 3){
                            mDataList = (ArrayList<CheckOrderRowsBean.OrderImages>) bean.orderImages.subList(0,3);
                        } else{
                            mDataList = bean.orderImages;
                        }
                    }
                    rv.setAdapter(new OrderProductAdapter(mDataList));
                    rv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));

                }else {
                    CheckOrderRowsBean.OrderImages data = bean.orderImages.get(0);
                    groupOne.setVisibility(View.VISIBLE);
                    rv.setVisibility(View.GONE);
                    baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + data.getImageUrl(), R.drawable.jiazaitu_min);
                    baseViewHolder.setText(R.id.tv_title,data.getProductName());
                    baseViewHolder.setText(R.id.tv_specifications, data.getSpec());
                    baseViewHolder.setText(R.id.tv_goods_num, "x"+data.getProductAmount());
                }
            }else {
                groupOne.setVisibility(View.INVISIBLE);
                rv.setVisibility(View.GONE);
            }

            //第三方药店
            TextView orderName = baseViewHolder.getView(R.id.tv_shop_title);
            ImageView arrow = baseViewHolder.getView(R.id.iv_arrow);
            arrow.setVisibility(View.GONE);
            if (bean.getIsParent().equals("1")) {
                orderName.setText("药帮忙");
                orderName.setOnClickListener(v -> {
                });
            } else {
                arrow.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(bean.origName)) {
                    orderName.setText(bean.origName);
                } else {
                    orderName.setText(bean.orderNo);
                }
                orderName.setOnClickListener(v -> {
                    OrderListReport.trackOrderItemShopTextBtnClick(mContext, bean, baseViewHolder.getBindingAdapterPosition());
                    if (bean.isThirdCompany == 1) {
                        RoutersUtils.open("ybmpage://shopactivity?orgId=" + bean.orgId);
                    } else {
                        RoutersUtils.open("ybmpage://searchresult?isThirdCompany=0&shopCode=" + bean.shopCode);
                    }
                });
            }

            OrderActionLayout layout = baseViewHolder.getView(R.id.ral_btn);
            bean.appraiseStatusFlag = orderState.equals("101");
            // 全部、配送中、他人代付、待支付项显示
            layout.setReceiveMoneyAccountBtnVisibility(bean.status == 10 && bean.payType == 3 && (TextUtils.equals(orderState, "0") || TextUtils.equals(orderState, "10")));
            layout.bindData(bean, baseViewHolder.getLayoutPosition(), true);
            layout.setOnReceiveMoneyAccountListener(() -> {
                if (accountInfoViewModel != null) {
                    showProgress();
                    accountInfoViewModel.getReceiveMoneyAccountInfo(bean.orderNo);
                }
            });
            //设置卖家备注
            ConstraintLayout clRemark = baseViewHolder.getView(R.id.cl_remark);
            if (!TextUtils.isEmpty(bean.sellerRemark)) {
                TextView tvRemarkDes = baseViewHolder.getView(R.id.tv_remark_des);
                tvRemarkDes.setText(bean.sellerRemark);
                clRemark.setVisibility(View.VISIBLE);
            } else {
                clRemark.setVisibility(View.GONE);
            }

            if (traceProductData.get(baseViewHolder.getBindingAdapterPosition()) == null
                    && bean.hasOrderExceptionFlag) {
                aptitudeView.setCanTrack(true);
                traceProductData.put(baseViewHolder.getBindingAdapterPosition(), bean.orderNo);
                HashMap<String, String> trackMap = new HashMap<>();
                trackMap.put("order_no", bean.orderNo);
                trackMap.put("system_reminder", !TextUtils.isEmpty(bean.sysException)?"1": "0");
                trackMap.put("business_reminder", !TextUtils.isEmpty(bean.supplierException)?"1": "0");
                trackMap.put("page_source", "order_list");
                XyyIoUtil.track("Qualification_Exception_Reminder_Exposure", trackMap);
            }

            aptitudeView.setData(bean);
            aptitudeView.setMerchantExceptionCheckCallback(s -> {
                handleSystemAndMerchantException(mContext, s, bean.orderNo);
                return null;
            });
            aptitudeView.setSystemExceptionCheckCallback(s -> {
                handleSystemAndMerchantException(mContext, s, bean.orderNo);
                return null;
            });
            aptitudeView.setVisibility(bean.hasOrderExceptionFlag? View.VISIBLE: View.GONE);
            aptitudeView.setFromPage("order_list");

        }
    }

    public static class OrderProductAdapter extends YBMBaseAdapter<CheckOrderRowsBean.OrderImages>{

        public OrderProductAdapter(ArrayList<CheckOrderRowsBean.OrderImages> dataList) {
            super(R.layout.item_order_list_product, dataList);
        }

        @Override
        protected void bindItemView(YBMBaseHolder baseViewHolder, CheckOrderRowsBean.OrderImages data) {
            baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + data.getImageUrl(), R.drawable.jiazaitu_min);
            baseViewHolder.setText(R.id.tv_goods_num, "x"+data.getProductAmount());
        }

        @Override
        public int getLayoutResId() {
            return R.layout.item_order_list_product;
        }
    }
}
