package com.ybmmarket20.bean

import java.math.BigDecimal

/**
 * 随心拼加购数据
 */
data class SpellGroupRecommendGoodsBean(
    //随心拼商品集合
    var rowsBean: MutableList<SpellGroupGoodsItem> = mutableListOf(),
    //主品
    var mainRowsBean: SpellGroupGoodsItem?,
    //商品id和加购数量的映射
    var goodsIdMapping: HashMap<String, Int>,
    var cartGoodsInfo: CartGoodsInfo?,
    //是否需要从服务端拉取数据
    var isUpdateData: Boolean = false
) {
    constructor() : this(mutableListOf(), null, hashMapOf(), CartGoodsInfo())

    /**
     * 获取带主品信息的CartGoodsInfo
     */
    fun getCartInfoWithMainRowsBean(): CartGoodsInfo {
        //随心拼商品总价格
        val suixinpinPrice = BigDecimal(cartGoodsInfo?.totalPrice?: "0")
        //主品总价格
        val mainGoodsPrice = BigDecimal(mainRowsBean?.totalPrice?: "0")
        return CartGoodsInfo(
            cartGoodsInfo?.goodsCategoriesCount?.plus(1) ?: 1,
            (cartGoodsInfo?.goodsTotalCount?: 0) + (mainRowsBean?.goodsSelectedCount ?: 0),
            suixinpinPrice.plus(mainGoodsPrice).toString()
        )
    }
}

/**
 * 加购的商品信息描述
 */
data class CartGoodsInfo(
    //商品种类数量
    var goodsCategoriesCount: Int,
    //商品总数量
    var goodsTotalCount: Int,
    //小计
    var totalPrice: String
) {
    constructor() : this(0, 0, "0")
}
