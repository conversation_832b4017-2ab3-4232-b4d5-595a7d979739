package com.ybmmarket20.bean;

import com.ybmmarketkotlin.bean.CouponDialogData;

import java.util.List;

/**
 * Created by asus on 2016/4/13.
 */
public class CommonDialog  {
    public String title;//可为空
    public String msg;
    public String wph;//宽高比，可为空
    public String action;//只要有就会执行，可为空
    public int style;//10 toast 20 对话框 如果是偶数就是强制确认对话框，其它地方不能取消
    public String sceneType = "99"; //1、首页 2、支付结果页 3.支付成功自动发券 99、其他
    public List<ModuleViewItem> btnActions;//按钮1个或者2个，如果是对话框就必需要有
    public String pageId;//弹窗需要传入参数
    public CouponDialogData couponDialogData;
    public int isClosePage; //0 不关闭， 1 关闭
    public int isCancelDialog; //0 可取消，1 不可取消

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CommonDialog that = (CommonDialog) o;

        if (style != that.style) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (msg != null ? !msg.equals(that.msg) : that.msg != null) return false;
        if (wph != null ? !wph.equals(that.wph) : that.wph != null) return false;
        if (action != null ? !action.equals(that.action) : that.action != null) return false;
        return btnActions != null ? btnActions.equals(that.btnActions) : that.btnActions == null;
    }

    @Override
    public int hashCode() {
        int result = title != null ? title.hashCode() : 0;
        result = 31 * result + (msg != null ? msg.hashCode() : 0);
        result = 31 * result + (wph != null ? wph.hashCode() : 0);
        result = 31 * result + (action != null ? action.hashCode() : 0);
        result = 31 * result + style;
        result = 31 * result + (btnActions != null ? btnActions.hashCode() : 0);
        return result;
    }
}
