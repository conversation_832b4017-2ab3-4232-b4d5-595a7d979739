package com.ybmmarket20.bean.cart;

import com.ybmmarket20.bean.CartActivityBean;

import java.util.List;

/*
* 公司-商城维度
* */
public class CartShopList {

    private int isHaveVoucher;//是否有店铺优惠券 0无 1有
    private String shopCode;//店铺编码
    private String shopName;//店铺名称
    private String shopType;//店铺类型
    private List<CartShoppingGroupFrontBean> shoppingGroupFrontDtos;//商品分组
    private String voucherUrl;
    private int selectStatus;// 非自营状态:0-未选中，1-选中
    private String appLinkUrl;//跳转shop商城
    private CartActivityBean returnVoucherInfo;//优惠券信息
    public int freightTipsShowStatus;//控制是否展示运费提示语, 0: 不展示；1：展示
    public String freightTips;//提示语
    private List<MarketingTipsBean> marketingTipsList; //购物车优惠券列表（老的优惠券逻辑保留，后续需求拓展放这里面）
    public String originalShopCode;
    public String originalShopName;

    public List<MarketingTipsBean> getMarketingTipsList() {
        return marketingTipsList;
    }

    public void setMarketingTipsList(List<MarketingTipsBean> marketingTipsList) {
        this.marketingTipsList = marketingTipsList;
    }

    public int getIsHaveVoucher() {
        return isHaveVoucher;
    }

    public void setIsHaveVoucher(int isHaveVoucher) {
        this.isHaveVoucher = isHaveVoucher;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public List<CartShoppingGroupFrontBean> getShoppingGroupFrontDtos() {
        return shoppingGroupFrontDtos;
    }

    public void setShoppingGroupFrontDtos(List<CartShoppingGroupFrontBean> shoppingGroupFrontDtos) {
        this.shoppingGroupFrontDtos = shoppingGroupFrontDtos;
    }

    public String getVoucherUrl() {
        return voucherUrl;
    }

    public void setVoucherUrl(String voucherUrl) {
        this.voucherUrl = voucherUrl;
    }

    public int getSelectStatus() {
        return selectStatus;
    }

    public void setSelectStatus(int selectStatus) {
        this.selectStatus = selectStatus;
    }

    public String getAppLinkUrl() {
        return appLinkUrl;
    }

    public void setAppLinkUrl(String appLinkUrl) {
        this.appLinkUrl = appLinkUrl;
    }

    public CartActivityBean getReturnVoucherInfo() {
        return returnVoucherInfo;
    }

    public void setReturnVoucherInfo(CartActivityBean returnVoucherInfo) {
        this.returnVoucherInfo = returnVoucherInfo;
    }

}
