package com.ybmmarket20.bean;

import java.util.ArrayList;
import java.util.List;

/**
 *  查询赠品池
 */
public class SearchGiftSelectResponseBean extends BaseSearchResultBean {

    public List<SearchRowsBean> rows;
    public String promoDesc; //活动描述文案
    public FilterConditions filterConditions; //反聚

    @Override
    public List<RowsBean> getDataList() {
        List<RowsBean> list = new ArrayList<>();
        if (rows == null) return list;
        for (SearchRowsBean row : rows) {
            if (row.getCardType() == 1) {
                list.add(row.getProductInfo());
            }
        }
        return list;
    }

    public class FilterConditions{
        public ArrayList<SearchFilterBean> specList; //规格列表
        public ArrayList<ManufacturersBean> manufacturerList; //厂家列表
    }
}
