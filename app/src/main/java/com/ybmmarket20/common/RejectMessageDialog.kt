package com.ybmmarket20.common

import android.content.Context
import android.widget.LinearLayout
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils
import java.lang.Exception

/**
 * 驳回原因弹框
 */
class RejectMessageDialog(rejectContext: Context) : AlertDialogEx(rejectContext) {

    override fun create() {
        super.create()
        try {
            val messageContent: LinearLayout = dialogView.findViewById(R.id.li_dialog_content)
            messageContent.setPadding(ConvertUtils.dp2px(10F), 0, ConvertUtils.dp2px(10F), 0)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}