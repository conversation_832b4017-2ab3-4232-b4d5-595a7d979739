package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import com.ybmmarket20.R;
import com.ybmmarket20.common.util.ConvertUtils;


public class WebViewProgressBar extends View {
    private int progress = 1;
    private int height = 3;
    private Paint paint;
    private final static int colors[] = new int[]{};
    public WebViewProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        height = ConvertUtils.dp2px(1);
        paint=new Paint(Paint.DITHER_FLAG);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(height);
        paint.setAntiAlias(true);
        paint.setColor(context.getResources().getColor(R.color.base_colors));
    }

    public WebViewProgressBar(Context context) {
        this(context,null);
    }
    public void setProgress(int progress){
        this.progress = progress;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawRect(0, 0, getWidth() * progress / 100, height, paint);
    }

}