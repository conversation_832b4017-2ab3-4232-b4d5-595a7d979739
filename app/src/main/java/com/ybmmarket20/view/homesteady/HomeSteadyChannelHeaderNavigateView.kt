package com.ybmmarket20.view.homesteady

import android.content.Context
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.util.AttributeSet
import com.ybmmarket20.bean.homesteady.ChannelNavigation

/**
 * <AUTHOR>
 * @date 2020-05-13
 * @description  频道页头部推荐按钮
 */
class HomeSteadyChannelHeaderNavigateView(context: Context, attributeSet: AttributeSet): RecyclerView(context, attributeSet), IHomeSteady {

    var mAdapter: HomeSteadyChannelNavigationAdapter? = null
    var data = mutableListOf<ChannelNavigation>()

    override fun initPlaceHold() {
        setChannelNavigateData(generateData(), null)
    }

    fun setChannelNavigateData(list: MutableList<ChannelNavigation>?, title: String?) {
        list?.also {
            data.clear()
            data.addAll(list)
            if(adapter == null) {
                mAdapter = HomeSteadyChannelNavigationAdapter(context, data, title)
                layoutManager = GridLayoutManager(context, 5)
                adapter = mAdapter
            } else mAdapter?.notifyDataSetChanged()
        }
    }

    /**
     * 占位数据
     */
    private fun generateData(): MutableList<ChannelNavigation> = mutableListOf<ChannelNavigation>().also {it ->
        (0 until 5).forEach{ _ -> it.add(ChannelNavigation())}
    }
}