package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.TextColorChangeUtils
import kotlinx.android.synthetic.main.prompt_popup.view.*


class CertificationPromptPopupWindow( context: Context, isShowSuccess:Boolean) : PopupWindow() {

    interface PopupListener {
        fun onDismiss()
    }

    var listener: PopupListener? = null

    init {
        contentView = LayoutInflater.from(context).inflate(R.layout.prompt_popup, null, false)
        width = ViewGroup.LayoutParams.WRAP_CONTENT
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        isOutsideTouchable = true
//        isTouchable = true
        isFocusable = true
        update()
        contentView?.bt_close?.setOnClickListener { dismiss() }
        if (isShowSuccess) {
            TextColorChangeUtils.setInterTextColor(context, contentView?.tv_des, "新增", "“被委托人信息确认”", "功能", R.color.color_1E1E24)
        } else {
            TextColorChangeUtils.setInterTextColor(context, contentView?.tv_des, "被委托人信息", "确认失败", "", R.color.detail_tv_color_ff2121)
        }
    }

    companion object {
        fun show(parent: View, x: Int, y: Int, listener: PopupListener?,isShowSuccess:Boolean): CertificationPromptPopupWindow {
            val popupWindow = CertificationPromptPopupWindow(parent.context,isShowSuccess)
            popupWindow.listener = listener
            popupWindow.setOnDismissListener { listener?.onDismiss() }
            popupWindow.showAtLocation(parent, Gravity.TOP or Gravity.RIGHT, ConvertUtils.getScreenWidth() - parent.right + x, 60 + parent.height + y)
            return popupWindow
        }

    }

}