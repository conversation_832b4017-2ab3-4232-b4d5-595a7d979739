package com.ybmmarket20.view;

import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_CHECK_REFUND;
import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_PAY_FOR_ANOTHER;
import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_UPLOAD_ELEC_CERTIFICATE;
import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_VIEW_APPLY_INVOICE;
import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_VIEW_ELEC_CERTIFICATE;
import static com.ybmmarket20.business.comment.event.OrderActionLayoutListener.TYPE_VIEW_INVOICE;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.bean.OrderActionBean;
import com.ybmmarket20.business.comment.event.OrderActionLayoutListener;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.utils.DateTimeUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

/**
 * 订单操作按钮
 */

/**
 * /**
 * 我的订单列表
 * 订单状态：
 * 2017-4-28 更新增加完整订单状态，
 * 2017-11-20 更新出库中隐藏申请退款功能
 * 2018-5-21 增加订单详情入库价格查看 订单在待支付、与取消 不显示其它都显示,货到付款订单审核中增加取消订单的按钮
 * <p>
 * 1 审核中，订单审核中 action 再次购买，申请退款/取消订单（全,只有详情有,当为货到付款的时候申请退款修改为取消订单）
 * 7 出库中  action 再次购买，申请退款（全,只有详情有）
 * 2 配送中 action 再次购买，确认收货，申请退款（选择对话框,只有详情有）
 * 3 已完成，action 再次购买，领取余额，申请退款（部分，只有详情有），可能还有查看退款功能（列表详情都有）
 * 4 取消 action 再次购买
 * 5 已删除 action 再次购买
 * 6 已拆单 action 再次购买
 * 10 待支付，待付款 action 取消订单，立即支付
 * 21 已拒签 action 再次购买，申请退款（只有详情有）
 * 20 已送达 action 再次购买，确认收货，申请退货（部分，只有详情有）
 * 90 已申请退款 退款审核中 action 查看退款，再次购买
 * 91 退款完成 action 查看退款，再次购买
 * <p>
 * add 2020-8-27 目前线上只有这些信息有效
 * 1-待配送，
 * 2-配送中，
 * 3-已配送，
 * 4-取消，
 * 5-已删除,
 * 6-已拆单,
 * 7-出库中,
 * 10-未支付,
 * 90-退款审核中, 服务端无用，提交退款申请后不会变成此状态
 * 91-已退款
 */
public class OrderActionLayout extends LinearLayout {
//    private TextView tvRefund;

    private OrderActionBean bean;

    //从 右 -> 左 依次是 1-5  根据 bt 内部文字做事件判断
    private Button bt1;
    private Button bt2;
    private Button bt3;
    private Button bt4;
    private Button bt5;
    private Button bt6;
    private TextView tvMore;
    private Button receiveMoneyAccountBtn;
    private String string;
    private long endTime;
    private int[] remainTime;
    private Handler limitHandler = new Handler();
    protected boolean autoRoll;
    private boolean isShowCheckRepositoryPrice = false;
    private OnReceiveMoneyAccountListener mListener;
    private boolean needResetBtn = true; //是否需要重置按钮样式
    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
//            if (tvRefund == null) {
//                return;
//            }
            if (limitHandler != null) {
                limitHandler.removeCallbacks(runnable);
            }
            if (!autoRoll) {
                limitHandler.removeCallbacks(runnable);
                return;
            }
            if (remainTime == null) {
                remainTime = DateTimeUtil.getRemainTime(endTime);
            } else {
                remainTime[3] -= 1;
                if (remainTime[3] == -1) {
                    remainTime[2] -= 1;
                    remainTime[3] = 59;
                    if (remainTime[2] == -1) {
                        remainTime[1] -= 1;
                        remainTime[2] = 59;
                        if (remainTime[1] == -1) {
                            remainTime[0] -= 1;
                            remainTime[1] = 23;
                            if (remainTime[0] == -1) {//结束了
                                remainTime = new int[]{0, 0, 0, 0};
                            }
                        }
                    }
                }
            }
            if (remainTime[0] <= 0 && remainTime[1] <= 0 && remainTime[2] <= 0 && remainTime[3] <= 0) { // 验证结束时间
//                tvRefund.setText("已结束");
                return;
            }
            String hh = "";
            String ff = "";
            String ss = "";
            if ((remainTime[0] * 24 + remainTime[1]) < 10) {
                hh = "0" + (remainTime[0] * 24 + remainTime[1]);
            } else {
                hh = "" + (remainTime[0] * 24 + remainTime[1]);
            }
            if (remainTime[2] < 10) {
                ff = "0" + remainTime[2];
            } else {
                ff = "" + remainTime[2];
            }
            if (remainTime[3] < 10) {
                ss = "0" + remainTime[3];
            } else {
                ss = "" + remainTime[3];
            }
            final String timeTitle = "剩余支付时间\n";
            final String timeStr = timeTitle + hh + ":" + ff + ":" + ss;
            SpannableStringBuilder builder = new SpannableStringBuilder(timeStr);
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(Color.rgb(255, 0, 0));
            builder.setSpan(foregroundColorSpan, 7, timeStr.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
//            tvRefund.setText(builder);
//            tvRefund.setGravity(Gravity.START);
            limitHandler.postDelayed(runnable, 1000);
        }
    };
    private CountDownTimer remainingTimer ; //剩余代支付时间倒计时
    private ArrayList<String> mMoreTextList = new ArrayList<>();

    public OrderActionLayout(Context context) {
        this(context, null);
    }

    public OrderActionLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OrderActionLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        if (getLayoutId() > 0) {
            LayoutInflater.from(getContext()).inflate(getLayoutId(), this);
        }
        init();
    }

    private OrderActionLayoutListener orderActionLayoutListener = new OrderActionLayoutListener();

    protected void init() {
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER_VERTICAL);
        bt1 = getView(R.id.bt_1);
        bt2 = getView(R.id.bt_2);
        bt3 = getView(R.id.bt_3);
        bt4 = getView(R.id.bt_4);
        bt5 = getView(R.id.bt_5);
        bt6 = getView(R.id.bt_6);
        tvMore = getView(R.id.tv_more);
        receiveMoneyAccountBtn = getView(R.id.btn_check_receive_money_account);
        //tvRefund = getView(R.id.tv_refund);
        string = getResources().getString(R.string.pay_text_color);

        bt1.setOnClickListener(orderActionLayoutListener);
        bt2.setOnClickListener(orderActionLayoutListener);
        bt3.setOnClickListener(orderActionLayoutListener);
        bt4.setOnClickListener(orderActionLayoutListener);
        bt5.setOnClickListener(orderActionLayoutListener);
        bt6.setOnClickListener(orderActionLayoutListener);
        receiveMoneyAccountBtn.setOnClickListener(v -> {
            if (mListener != null) mListener.onReceiveMoneyAccount();
        });
        tvMore.setOnClickListener(v -> {
            OrderActionPopWindow popWindow = new OrderActionPopWindow(getContext());
            popWindow.setData(mMoreTextList,bean);
            popWindow.showPopup(tvMore,AppUtilKt.getDp(-3),0);
        });
        //tvRefund.setOnClickListener(orderActionLayoutListener);
    }

    protected int getLayoutId() {
        return R.layout.order_action_layout;
    }

    public <T extends View> T getView(int viewId) {
        if (this == null) {
            return null;
        }
        try {
            return (T) this.findViewById(viewId);
        } catch (Throwable e) {
            return null;
        }
    }

    public void bindData(CheckOrderRowsBean bean, int position, boolean isList) {
        if (bean == null) {
            return;
        }
        orderActionLayoutListener.mIOrderListItem = bean;
        orderActionLayoutListener.mPosition = position;
        needResetBtn = true;
        OrderActionBean actionBean = rows2Action(bean);
        actionBean.position = position;
        bindData(actionBean, isList);
    }

    /**
     * 初始化bt状态
     */
    private void resetBtStatus() {
        bt1.setVisibility(View.GONE);
        bt2.setVisibility(View.GONE);
        bt3.setVisibility(View.GONE);
        bt4.setVisibility(View.GONE);
        bt5.setVisibility(View.GONE);
        bt6.setVisibility(View.GONE);
        tvMore.setVisibility(View.GONE);
//        tvRefund.setVisibility(GONE);
        resetBg(bt1);
        resetBg(bt2);
        resetBg(bt3);
        resetBg(bt4);
        resetBg(bt5);
        resetBg(bt6);
        resetBg(receiveMoneyAccountBtn);
    }

    private void resetBg(TextView view) {
        setNormalBtStyle(view);
    }

    public void bindData(OrderActionBean bean, boolean isList) {
        removeCountDownTimer();
        bindData(bean, isList, false);
    }

    /**
     * @param bean
     * @param isList 该控件在订单类别 or 订单详情
     */
    public void bindData(OrderActionBean bean, boolean isList, boolean isKaUser) {
        this.bean = bean;
        orderActionLayoutListener.mIOrderListItem = bean;
        mMoreTextList.clear();
        orderActionLayoutListener.setOrderActionBean(bean);
        orderActionLayoutListener.isList1 = isList;
        resetBtStatus();

        if (bean != null) {
            if (bean.refundCount > 0 && isList) {
                bt6.setVisibility(View.VISIBLE);
                bt6.setOnClickListener(orderActionLayoutListener);
            }
            switch (bean.status) {
                case 1:
                    // 订单审核中状态
                    bt1.setVisibility(View.VISIBLE);
                    bt2.setVisibility(View.VISIBLE);
                    if (bean.paytype == 2) {
                        bt2.setText("取消订单");
                    } else {
                        bt2.setText("申请退款");
                    }
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
                    if (isList) {
                        if (bean.paytype != 2) {
                            bt2.setVisibility(View.GONE);
                        } else {

                            bt2.setVisibility(View.VISIBLE);
                        }
                    } else {
                        //显示查看入库价格
                        bt3.setVisibility(View.VISIBLE);
                        bt3.setText("查看入库价");

                        //1 在线支付 2货到付款 3 线下转账
                        if (bean.paytype != 2) {
                            //退款按钮隐藏
                            if (bean.isShowRefund == 1 || isKaUser) { //订单审核中增加ka用户详情隐藏申请退款 退货
                                bt2.setVisibility(View.GONE);
                            } else {
                                bt2.setVisibility(View.VISIBLE);
                            }

                        }
                    }

                    break;
                case 7://action 再次购买，申请退款（只有详情有）
                    //出库中状态
                    bt1.setVisibility(View.VISIBLE);
                    bt2.setVisibility(View.VISIBLE);
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
                    bt2.setText("申请退款");
                    if (bean.isShowRefund == 1) {
                        bt2.setVisibility(View.GONE);
                    }
                    setLightGreen(bt1);
                    if (isList) {
                        bt2.setVisibility(View.GONE);
                    } else {
                        //显示查看入库价格
                        bt3.setVisibility(View.VISIBLE);
                        bt3.setText("查看入库价");
                    }
                    break;
                case 2://要有是否收到货的选择对话框
                    //配送中状态
                    bt1.setVisibility(View.VISIBLE);
                    bt2.setVisibility(View.VISIBLE);
                    bt3.setVisibility(View.VISIBLE);

                    bt1.setText("再次购买");
                    setLightGreen(bt1);

                    bt2.setText("申请退款");
                    bt3.setVisibility(View.VISIBLE);
                    if (bean.canConfirmReceipt == 1) {
                        bt3.setText("确认收货");
                        setGreenBtStyle(bt3);
                    } else {
                        bt3.setVisibility(View.GONE);
                    }

                    if (isList) {
                        bt2.setVisibility(View.GONE);
                        bt4.setVisibility(View.VISIBLE);

                        //设置发票按钮的状态
                        setInvoiceButtonState(bean.getInvoiceState(), bt4);
                        bt3.setVisibility(View.VISIBLE);
                        //此处列表页的顺序和其他不一样 换位
                        bt3.setText("再次购买");
                        setLightGreen(bt3);
                        if (bean.canConfirmReceipt == 1) {
                            bt1.setText("确认收货");
                            setGreenBtStyle(bt1);
                        } else {
                            bt1.setVisibility(View.GONE);
                        }

                        if (bt1.getVisibility() == View.VISIBLE && bt6.getVisibility() == View.VISIBLE){
                            //此时会有按钮 退款/售后、查看发票、再次购买、确认收货  大于4个 把退款/售后拿到更多里面去
                            tvMore.setVisibility(View.VISIBLE);
                            bt6.setVisibility(View.GONE);
                            mMoreTextList.add(TYPE_CHECK_REFUND);
                        }

                    } else if(bean.isApplyForAfterSales()) {
                        bt2.setText("申请售后");
                        bt2.setVisibility(View.VISIBLE);
                        //显示查看入库价格
                        bt4.setVisibility(View.VISIBLE);
                        bt4.setText("查看入库价");
                    } else {
                        //退款按钮隐藏
                        if (bean.isShowRefund == 1 || isKaUser) { //配送中增加ka用户详情隐藏申请退款 退货
                            bt2.setVisibility(View.GONE);
                        } else {
                            bt2.setVisibility(View.VISIBLE);
                        }
                        //显示查看入库价格
                        bt4.setVisibility(View.VISIBLE);
                        bt4.setText("查看入库价");
                    }
                    break;
                case 3://申请退货(详情有),确认收货，再次购买
                    //已完成状态
                    bt1.setVisibility(View.VISIBLE);
                    bt2.setVisibility(View.VISIBLE);
                    bt3.setVisibility(View.VISIBLE);
                    bt4.setVisibility(View.VISIBLE);

                    //是列表
                    if (isList) {
                        bt1.setText("再次购买");
                        setLightGreen(bt1);

                        //设置发票按钮的状态
                        setInvoiceButtonState(bean.getInvoiceState(), bt2);

                        if (bean.appraiseStatusFlag) {
                            switch (bean.appraiseStatus) {
                                case 1:
                                    bt3.setVisibility(View.VISIBLE);
                                    bt3.setText("评价");
                                    break;
                                default:
                                    bt3.setVisibility(View.GONE);
                            }
                        } else {

                            switch (bean.appraiseStatus) {
                                case 1:
                                    bt3.setVisibility(View.VISIBLE);
                                    bt3.setText("评价");
                                    break;
                                case 2:
                                    bt3.setVisibility(View.VISIBLE);
                                    bt3.setText("查看评价");
                                    break;
                                case 3:
                                    bt3.setVisibility(View.GONE);
                                    break;
                            }
                        }

                        if (bean.balanceStatus == 0) {//没有领取
                            bt4.setText("领取余额");
                            setGreenBtStyle(bt4);
                        } else if (bean.balanceStatus == 1) {//已经领取
                            bt4.setText("查看余额");
                        } else {//不能领取
                            bt4.setVisibility(View.GONE);
                        }

                        int currentCount = 2; //bt1 和 bt2
                        //做多显示3个按钮 然后多的移入更多里面去
                        if (bt6.getVisibility() == View.VISIBLE){
                            currentCount++;
                        }
                        if (bt3.getVisibility() == View.VISIBLE){
                            currentCount++;
                            if (currentCount > 3){
                                mMoreTextList.add(bt3.getText().toString());
                                bt3.setVisibility(View.GONE);
                            }
                        }
                        if (bt4.getVisibility() == View.VISIBLE){
                            currentCount++;
                            if (currentCount > 3){
                                mMoreTextList.add(bt4.getText().toString());
                                bt4.setVisibility(View.GONE);
                            }
                        }

                        if (!mMoreTextList.isEmpty()){
                            tvMore.setVisibility(View.VISIBLE);
                        }

                    } else {

                        bt2.setText("再次购买");
                        setLightGreen(bt2);

                        if (bean.isApplyForAfterSales()) {
                            bt3.setText("申请售后");
                        } else {
                            bt3.setText("申请退货");
                        }

                        switch (bean.appraiseStatus) {
                            case 1:
                                bt1.setVisibility(View.VISIBLE);
                                bt1.setText("评价");
                                break;
                            case 2:
                                bt1.setVisibility(View.VISIBLE);
                                bt1.setText("查看评价");
                                break;
                            case 3:
                                bt1.setVisibility(View.GONE);
                                break;
                        }

                        if (bean.balanceStatus == 0) {//没有领取
                            bt4.setText("领取余额");
                            setGreenBtStyle(bt4);
                        } else if (bean.balanceStatus == 1) {//已经领取
                            bt4.setText("查看余额");
                        } else {//不能领取
                            bt4.setVisibility(View.GONE);
                        }

                        //退款按钮隐藏
                        if (bean.isShowRefund == 1 || isKaUser) { //已完成增加ka用户详情隐藏申请退款 退货
                            if (bean.isApplyForAfterSales()) {
                                bt3.setVisibility(View.VISIBLE);
                            } else {
                                bt3.setVisibility(View.GONE);
                            }
                        } else {
                            bt3.setVisibility(View.VISIBLE);
                        }
                        bt4.setVisibility(View.GONE);
                        switch (bean.appraiseStatus) {
                            case 1:
                                bt1.setVisibility(View.VISIBLE);
                                bt1.setText("评价");
                                break;
                            case 2:
                                bt1.setVisibility(View.VISIBLE);
                                bt1.setText("查看评价");
                                break;
                            case 3:
                                bt1.setVisibility(View.GONE);
                                break;
                        }
                        //显示查看入库价格
                        bt4.setVisibility(View.VISIBLE);
                        bt4.setText("查看入库价");
                    }

                    break;
                case 4://再次购买
                case 5:
                case 6:
                    bt1.setVisibility(View.VISIBLE);
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
                    break;
                case 10:// 未支付：取消订单，去支付
                    // 非在线支付
                    bt1.setVisibility(View.VISIBLE);
                    if (bean.paytype != 1) {
                        if (isList) {
                            // 订单列表列表： 上传电汇凭证  暂时不用sho
                            // wUploadEvidenceBtn 这个字段判断了
//                            if (bean.showUploadEvidenceBtn) {
//
//                            } else {
//                                bt1.setVisibility(View.GONE);
//                            }
                            switch (bean.evidenceVerifyStatus){
                                case  0:
                                    processCountDown(bt1,TYPE_UPLOAD_ELEC_CERTIFICATE,isList);
//                                    bt1.setText(TYPE_UPLOAD_ELEC_CERTIFICATE);
                                    setGreenBtStyle(bt1);
                                    break;
                                case 1:
                                    bt1.setText(TYPE_VIEW_ELEC_CERTIFICATE);
                                    setGreenBtStyle(bt1);
                                    break;
                                default:
                                bt1.setVisibility(View.GONE);
                            }
                        } else {
                            // 非订单列表： 再次购买、取消订单、查看入库价(这种情况时 setRepositoryPrice这个方法
                            // bt3就是查看入库价，可以看上面的代码)

//                            bt1.setText("再次购买");

                            switch (bean.evidenceVerifyStatus){
                                case  0:
                                    bt1.setText(TYPE_UPLOAD_ELEC_CERTIFICATE);
                                    setGreenBtStyle(bt1);
                                    break;
                                case 1:
                                    bt1.setText(TYPE_VIEW_ELEC_CERTIFICATE);
                                    setGreenBtStyle(bt1);
                                    break;
                                default:
                                    bt1.setVisibility(View.GONE);
                            }
                            bt2.setVisibility(View.GONE);
                            bt3.setVisibility(View.VISIBLE);
                            bt4.setVisibility(View.VISIBLE);
                            bt4.setText("取消订单");
                            needResetBtn = false;
                        }
                    } else {
                        // 在线支付
                        if (isList) {
                            // 订单列表列表： 立即支付、找人代付
                            processCountDown(bt1,"去支付",isList);
                            setGreenBtStyle(bt1);
//                            bt1.setText("立即支付");
                            bt2.setVisibility("1".equals(bean.showOthersPayState) ? View.VISIBLE : GONE);
                            bt2.setText(TYPE_PAY_FOR_ANOTHER);
                        } else {
                            // 非订单列表
                            bt1.setText("立即支付");
                            setGreenBtStyle(bt1);
                            bt2.setVisibility(View.VISIBLE);
                            bt2.setText("取消订单");
                        }

                    }


                    break;
                case 21://再次购买，申请退款（只有详情有）
                    bt1.setVisibility(View.VISIBLE);

                    bt2.setText("申请退款");
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
                    if (!isList) {

                        bt2.setVisibility(View.VISIBLE);
                        bt3.setVisibility(View.VISIBLE);
                        bt3.setText("查看入库价");

                        //退款按钮隐藏
                        if (bean.isShowRefund == 1) {
                            bt2.setVisibility(View.GONE);
                        } else {
                            bt2.setVisibility(View.VISIBLE);
                        }

                    }
                    break;
                case 20://再次购买，确认收货，申请退货（部分，只有详情有）
                    bt1.setVisibility(View.VISIBLE);
                    bt3.setVisibility(View.VISIBLE);

                    bt3.setText("确认收货");
                    setGreenBtStyle(bt3);
                    bt2.setText("申请退货");
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
                    if (!isList) {
                        bt2.setVisibility(View.VISIBLE);
                        bt4.setVisibility(View.VISIBLE);
                        bt4.setText("查看入库价");

                        //退款按钮隐藏
                        if (bean.isShowRefund == 1) {
                            bt2.setVisibility(View.GONE);
                        } else {
                            bt2.setVisibility(View.VISIBLE);
                        }
                    }

                    break;
                case 90:
                case 91:
                case 93://action 再次购买,查看退款
                    //90:退款审核中
                    //91:已退款
                    bt1.setVisibility(View.VISIBLE);
                    bt1.setText("再次购买");
                    setLightGreen(bt1);
//                    tvRefund.setVisibility(View.VISIBLE);
                    if (!isList) {
                        bt2.setVisibility(View.VISIBLE);
                        bt2.setText("查看入库价");
                    }
                    if (bean.status == 91){ //退款已完成
                        bt3.setVisibility(View.VISIBLE);
                        if (bean.isApplyForAfterSales()) {
                            bt3.setText("申请售后");
                        } else if (bean.canFreightRefund()) {
                            bt3.setText("申请退款");
                        } else {
                            bt3.setVisibility(View.GONE);
                        }
                    }else {
                        bt3.setVisibility(View.GONE);
                    }
                    break;
            }

            if (bean.payTime > 0 && !isList && bean.status == 10) {
                endTime = bean.payTime;
//                tvRefund.setVisibility(View.VISIBLE);
                if (limitHandler != null) {
                    limitHandler.removeCallbacks(runnable);
                    autoRoll = true;
                    limitHandler.post(runnable);
                }
            } else {
//                tvRefund.setVisibility(View.GONE);
//                if (bean.isRefund && isList) {
//                    tvRefund.setVisibility(View.VISIBLE);
//                }
                endTime = 0;
                if (limitHandler != null) {
                    limitHandler.removeCallbacks(runnable);
                    autoRoll = false;
                }
            }
            if (bean.reminderStatus == 1) {
                bt5.setVisibility(View.VISIBLE);
                bt5.setText("提醒发货");
                needResetBtn = false;
            } else if (bean.reminderStatus == 2) {
                bt5.setVisibility(View.VISIBLE);
                bt5.setText("提醒发货进度");
                needResetBtn = false;
            }

//            if (needResetBtn) {
//                if (!isList) {
//                    resetBg(bt1);
//                    resetBg(bt2);
//                    resetBg(bt3);
//                    resetBg(bt4);
//                    resetBg(bt5);
//                    resetBg(bt6);
//                }
//                sortAndSetBg();
//            }
        }
    }

    private void setInvoiceButtonState(String state, Button btn) {
        switch (state) {
            case "0":
                btn.setText(TYPE_VIEW_APPLY_INVOICE);
                break;
            case "1":
                btn.setText(TYPE_VIEW_INVOICE);
                break;
            default:
                btn.setText(TYPE_VIEW_INVOICE);
        }
    }

    private void processCountDown(TextView textView,String prefixStr,Boolean isList) {
        if (!isList) return;

        long localDiff = System.currentTimeMillis() - bean.responseLocalTime;
        long leftTime = bean.countDownNewTime*1000 - localDiff;
        if (leftTime > 0) {
            CountDownTimer countDownTimer = new CountDownTimer(leftTime,1000L) {
                @Override
                public void onTick(long millisUntilFinished) {

                    // 可能出现RecycleView中复用时， 还没来得及移除 计时器又执行了一下 导致textView被错误赋值了
                    if (bean.status != 10) return; //待支付才倒计时

                    // 计算剩余的小时数
                    long hours = millisUntilFinished / (1000 * 60 * 60);
                    // 计算剩余的分钟数（去掉小时部分后的毫秒数）
                    long minutes = millisUntilFinished % (1000 * 60 * 60) / (1000 * 60);
                    // 计算剩余的秒数（去掉分钟部分后的毫秒数）
                    long seconds = millisUntilFinished % (1000 * 60) / 1000;

                    String hoursStr = "00";
                    String minutesStr = "00";
                    String secondsStr = "00";
                    if (hours < 10){
                        hoursStr = "0" + hours;
                    } else {
                        hoursStr = String.valueOf(hours);
                    }
                    if (minutes < 10){
                        minutesStr = "0" + minutes;
                    } else {
                        minutesStr = String.valueOf(minutes);
                    }
                    if (seconds < 10){
                        secondsStr = "0" + seconds;
                    } else {
                        secondsStr = String.valueOf(seconds);
                    }

                    textView.setText(prefixStr + hoursStr + ":" + minutesStr + ":" + secondsStr);
                }

                @Override
                public void onFinish() {
                    textView.setText(prefixStr+"00:00:00");
                }
            };
            remainingTimer = countDownTimer;
            countDownTimer.start();
        }else{
            textView.setText(prefixStr);
        }
    }

    public void removeCountDownTimer() {
        if (remainingTimer != null) {
            remainingTimer.cancel();
            remainingTimer = null;
        }
    }


    private void sortAndSetBg() {

        ArrayList<String> btStr = new ArrayList<>();

        if (bt1.getVisibility() == View.VISIBLE) {
            btStr.add(bt1.getText().toString());
        }
        if (bt2.getVisibility() == View.VISIBLE) {
            btStr.add(bt2.getText().toString());

        }
        if (bt3.getVisibility() == View.VISIBLE) {
            btStr.add(bt3.getText().toString());

        }
        if (bt4.getVisibility() == View.VISIBLE) {
            btStr.add(bt4.getText().toString());

        }
        if (bt5.getVisibility() == View.VISIBLE) {
            btStr.add(bt5.getText().toString());

        }

        Collections.sort(btStr, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {


                return getLevel(o2) - getLevel(o1);
            }

            private int getLevel(String o1) {

                if (o1.equals(OrderActionLayoutListener.TYPE_ORDER_RECEIVED) || o1.equals(OrderActionLayoutListener.TYPE_COMMENT) || o1.equals(OrderActionLayoutListener.TYPE_VIEW_COMMENT) || o1.equals(OrderActionLayoutListener.TYPE_PAY_NOW)) {
                    return 4;
                }

                if (o1.equals(OrderActionLayoutListener.TYPE_ORDER_CANCEL) || o1.equals(OrderActionLayoutListener.TYPE_BUY_AGAIN)) {

                    return 3;
                }

                if (o1.equals(OrderActionLayoutListener.TYPE_VIEW_STORE_PRICE)) {
                    return 2;
                }

                if (o1.equals(OrderActionLayoutListener.TYPE_ORDER_REFUND_GOODS) || o1.equals(OrderActionLayoutListener.TYPE_ORDER_REFUND_MONEY)) {
                    return 1;
                }

                return 0;
            }
        });

        for (int i = 0; i < btStr.size(); i++) {
            String s = btStr.get(i);

            if (OrderActionLayoutListener.TYPE_PAY_NOW.equals(s)) {
                setGreenBtStyle(bt1);
            }

            if (OrderActionLayoutListener.TYPE_COMMENT.equals(s) || OrderActionLayoutListener.TYPE_VIEW_COMMENT.equals(s) || OrderActionLayoutListener.TYPE_BUY_AGAIN.equals(s)) {
                if (bt1.getVisibility() == VISIBLE) {
                    setNormalBtStyle(bt1);
                }
                if (bean.status == 3) {
                    // 已完成订单，可能会出现以下情况，显示按顺序排列（从右到左）
                    // "评价"或"查看评价"、"再次购买" \"申请退货" \"查看入库价
                    setLightGreen(bt2);
                }
            }


        }

    }

    private void setGreenBtStyle(TextView bt) {
        bt.setTextColor(getResources().getColor(R.color.white));
        bt.setBackground(getResources().getDrawable(R.drawable.order_layout_green_border));
        bt.setTypeface(Typeface.DEFAULT_BOLD);

    }

    private void setLightGreen(TextView bt) {
        bt.setTextColor(getResources().getColor(R.color.color_00b955));
        bt.setBackground(getResources().getDrawable(R.drawable.order_gray_border_eaf9f1));
        bt.setTypeface(Typeface.DEFAULT_BOLD);
    }

    private void setNormalBtStyle(TextView bt) {
        bt.setTextColor(getResources().getColor(R.color.text_color_333333));
        bt.setBackground(getResources().getDrawable(R.drawable.order_normal_border));
        bt.setTypeface(Typeface.DEFAULT);
    }



//    private void setBlackBtStyle(Button bt) {
//        bt.setTextColor(getResources().getColor(R.color.text_292933));
//        bt.setBackground(getResources().getDrawable(R.drawable.order_gray_border_EAF9F1));
//    }
//
//    private void setMainBtStyle(Button bt) {
//        bt.setTextColor(getResources().getColor(R.color.white));
//        bt.setBackground(getResources().getDrawable(R.drawable.order_green_bg));
//    }

    private static OrderActionBean rows2Action(CheckOrderRowsBean bean) {
        if (bean == null) {
            return null;
        }
        OrderActionBean orderActionBean = new OrderActionBean(bean.id + "", bean.money + "", bean.payType, bean.status, bean.balanceStatus, bean.balanceText, bean.refundText,
                bean.refundCount, bean.canConfirmReceipt, bean.appraiseStatus,
                bean.appraiseStatusFlag, bean.orderNo, bean.isThirdCompany, bean.isShowRefund,
                bean.showOthersPayState, bean.showUploadEvidenceBtn, bean.transferInfoUrl,bean.evidenceVerifyStatus);
        orderActionBean.cashPayAmount = bean.cashPayAmount;
        orderActionBean.refundCount = bean.refundCount;
        orderActionBean.orderImages = bean.orderImages;
        orderActionBean.countDownNewTime = bean.countDownNewTime;
        orderActionBean.responseLocalTime = bean.responseLocalTime;
        orderActionBean.companyName = bean.companyName;
        orderActionBean.invoiceState= bean.invoiceState;

        return orderActionBean;

    }


    public void onDestroy() {
//        autoRoll = false;
//        if (limitHandler != null) {
//            limitHandler.removeCallbacks(runnable);
//        }
//        limitHandler = null;
//        runnable = null;
    }

    public void onResume() {
//        if (endTime > 0) {
//            if (limitHandler != null) {
//                autoRoll = true;
//                limitHandler.removeCallbacks(runnable);
//                if (runnable != null) {
//                    limitHandler.post(runnable);
//                }
//            }
//        }
    }

    public void onPause() {
//        autoRoll = false;
//        if (limitHandler != null) {
//            limitHandler.removeCallbacks(runnable);
//        }
    }

    /**
     * 设置是否显示入库价
     *
     * @param isShowCheckRepositoryPrice
     */
    public void setRepositoryPrice(boolean isShowCheckRepositoryPrice) {
        this.isShowCheckRepositoryPrice = isShowCheckRepositoryPrice;
        if (isShowCheckRepositoryPrice) {
            bt3.setVisibility(View.VISIBLE);
            bt3.setText("查看入库价");
        } else {
            bt3.setVisibility(View.GONE);
        }
    }

    /**
     * 是否显示查看收款账户
     * @param isVisibility 是否显示
     */
    public void setReceiveMoneyAccountBtnVisibility(boolean isVisibility) {
        receiveMoneyAccountBtn.setVisibility(isVisibility? View.VISIBLE: View.GONE);
    }

    /**
     * 监听查看收款账户监听
     * @param listener
     */
    public void setOnReceiveMoneyAccountListener(OnReceiveMoneyAccountListener listener) {
        mListener = listener;
    }

    public interface OnReceiveMoneyAccountListener {
        void onReceiveMoneyAccount();
    }


}
