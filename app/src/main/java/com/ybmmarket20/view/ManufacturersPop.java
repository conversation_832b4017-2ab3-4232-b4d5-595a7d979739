package com.ybmmarket20.view;

import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.mcxtzhang.indexlib.suspension.SuspensionDecoration;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ManufacturersBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * 厂家左边出来view
 */

public class ManufacturersPop {

    private LeftPopWindow popWindow;
    private LeftPopWindow.Listener<List<String>> listener;

    protected CommonRecyclerView rv;
    protected TextView mTvSideBarHint;
    protected EditText etSearch;
    protected ImageView ivDel;
    protected IndexBar mIndexBar;

    private List<ManufacturersBean> mDatas = new ArrayList<>();
//    protected SuspensionDecoration mDecoration;
    protected YBMBaseAdapter adapter;

    protected List<String> lastNames = new ArrayList<>();
    private boolean isAvailable = false;
    private boolean isPromotion = false;
    private boolean isClassA = false;
    private boolean isClassB = false;
    private boolean isClassRx = false;
    private boolean isClassElse = false;
    private String spec = "";
    private String shopCodes = "";
    private String priceRangeFloor = "";
    private String priceRangeTop = "";
    private String drugClassification = "";
    private String key = "";
    private String value = "";
    protected String lastName = "全部厂家";
    private boolean isPlan;
    private String planId;

    public ManufacturersPop() {
        init();
    }

    public ManufacturersPop(String planId) {
        init();
        isPlan = true;
        this.planId = planId;
    }


    public void init() {
        popWindow = new LeftPopWindow<ManufacturersBean>(R.layout.manufacturers_pop) {

            @Override
            protected void initView(View contentView) {

                ImageView ivBack = (ImageView) contentView.findViewById(R.id.iv_back);
                TextView tvTitle = (TextView) contentView.findViewById(R.id.tv_title);
                TextView tvRight = (TextView) contentView.findViewById(R.id.tv_right);

                tvTitle.setText("生产厂家");
                ivBack.setImageResource(R.drawable.icon_left);
                tvRight.setVisibility(View.VISIBLE);

                contentView.findViewById(R.id.iv_back).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                            ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).hideSoftInput(etSearch);
                        }
                        setIsEmpty();
                        popWindow.dismiss(false);
                    }
                });
                rv = (CommonRecyclerView) contentView.findViewById(R.id.rv_list);
                contentView.findViewById(R.id.view_bg).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                            ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).hideSoftInput(etSearch);
                        }
                        setIsEmpty();
                        popWindow.dismiss(false);
                    }
                });
                etSearch = (EditText) contentView.findViewById(R.id.et_search);
                ivDel = (ImageView) contentView.findViewById(R.id.iv_del);
                ivDel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etSearch.setText("");
                    }
                });
                etSearch.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        if (s != null && s.length() > 0) {//输入文字
                            ivDel.setVisibility(View.VISIBLE);
                            if (rv.isEnabled()) {//禁止下拉刷新
                                rv.setEnabled(false);
                            }
                            searchName(s.toString());
                        } else {//清空
                            ivDel.setVisibility(View.GONE);
                            setNewData(true, mDatas);
                        }
                    }

                    @Override
                    public void afterTextChanged(Editable s) {

                    }
                });
                tvRight.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (popWindow != null) {
                            setIsEmpty();
                            popWindow.onResult(lastNames);
                        }
                    }
                });
                mTvSideBarHint = (TextView) contentView.findViewById(R.id.tvSideBarHint);//HintTextView
                mIndexBar = (IndexBar) contentView.findViewById(R.id.indexBar);//IndexBar
                // mDecoration = new SuspensionDecoration(rv.getContext(), mDatas);
                // mDecoration.setColorTitleBg(com.ybm.app.utils.UiUtils.getColor(R.color.choose_eara_item_press_color));
                // mDecoration.setColorTitleFont(com.ybm.app.utils.UiUtils.getColor(R.color.text_9494A6));
                // rv.addItemDecoration(mDecoration);
                rv.setShowAutoRefresh(false);
                /*rv.setListener(new CommonRecyclerView.Listener() {
                    @Override
                    public void onRefresh() {
                        getData(false);
                    }

                    @Override
                    public void onLoadMore() {

                    }
                });*/
                adapter = new YBMBaseAdapter<ManufacturersBean>(R.layout.choose_item_shop, mDatas) {
                    @Override
                    protected void bindItemView(final YBMBaseHolder holder, final ManufacturersBean bean) {

                        TextView tv = holder.getView(R.id.tv);
                        tv.setActivated(lastNames.contains(bean.manufacturer));
                        tv.setText(bean.manufacturer);

                        tv.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                boolean activated = view.isActivated();
                                if (activated) {
                                    view.setActivated(false);
                                    if (lastNames != null) {
                                        lastNames.remove(bean.manufacturer);
                                    }
                                } else {
                                    view.setActivated(true);
                                    if (lastNames != null) {

                                        if (lastName.equals(bean.manufacturer)) {
                                            lastNames.clear();
                                        } else {
                                            if (lastNames.contains(lastName)) {
                                                lastNames.remove(lastName);
                                            }
                                        }
                                        lastNames.add(bean.manufacturer);
                                    }
                                    notifyDataSetChanged();
                                }
                            }
                        });
                    }
                };
                rv.setAdapter(adapter);
                rv.setLoadMoreEnable(false);
                //indexbar初始化
                mIndexBar.setmPressedShowTextView(mTvSideBarHint)//设置HintTextView
                        .setNeedRealIndex(true)//设置需要真实的索引
                        .setDataHelper(new IndexBarDataHelper())
                        .setSourceDatasAlreadySorted(false)
                        .setmLayoutManager((WrapLinearLayoutManager) rv.getLayoutManager())
                        .setIndexColor(R.color.base_colors_new).setPadding(ConvertUtils.dp2px(6))
                        .setHeaderViewCount(1);//设置RecyclerView的LayoutManager
            }
        };

        popWindow.setListener(new LeftPopWindow.Listener<List<String>>() {
            @Override
            public void onDismiss() {
                if (listener != null) {
                    listener.onDismiss();
                }
            }

            @Override
            public void onResult(List<String> list) {
                if (BaseYBMApp.getApp().getCurrActivity() != null && BaseYBMApp.getApp().getCurrActivity() instanceof BaseActivity) {
                    ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).hideSoftInput(etSearch);
                }
                popWindow.dismiss(false);
                if (listener != null) {
                    listener.onResult(list);
                }
            }
        });
        popWindow.setClassify(false);
    }

    /**
     * 如果list包含“全部厂家”就把list制空
     */
    private void setIsEmpty() {
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        if (lastNames.contains(lastName)) {
            lastNames.clear();
        }
    }

    /**
     * 移除指定的key
     *
     * @param key
     */
    public void removeKey(String key) {
        if (lastNames == null) return;
        if (lastNames.contains(key)) {
            lastNames.remove(key);
        }
    }

    public void setListener(LeftPopWindow.Listener<List<String>> listener) {
        if (popWindow == null) {
            init();
        }
        this.listener = listener;
    }

    private RequestParams getParams() {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        if (isPlan) {
            params.setUrl(AppNetConfig.PLAN_SCHEDULE_MANUFACTURER);
            params.put("planningScheduleId", planId);
        } else {
            params.setUrl(AppNetConfig.FIND_MANUFACTURER);
        }
        params.put("merchantId", merchantid);

        //全部分类 categoryId 搜索关键字 showName
        if (!TextUtils.isEmpty(key)) {
            params.put(key, value);
            LogUtils.e("manufacture : key = " + key + "; value = " + value);
        }
        //经营类型
        drugClassification = setDrugsClass();
        if (!TextUtils.isEmpty(drugClassification)) {
            params.put("drugClassificationStr", drugClassification);
        }
        //仅看有货
        if (isAvailable) {
            params.put("hasStock", "1");
        }
        //有促销
        if (isPromotion) {
            params.put("isPromotion", "1");
        }
        // 店铺code
        if (!TextUtils.isEmpty(shopCodes)) {
            params.put("shopCodes", shopCodes);
            LogUtils.e("manufacture : shopCodes = " + shopCodes + "; ");
        }
        // 规格
        if (!TextUtils.isEmpty(spec)) {
            params.put("spec", spec);
            LogUtils.e("manufacture : spec = " + spec + "; ");
        }
        //价格区间-最低价
        if (!TextUtils.isEmpty(priceRangeFloor)) {
            params.put("minPrice", priceRangeFloor);
        }
        //价格区间-最高价
        if (!TextUtils.isEmpty(priceRangeTop)) {
            params.put("maxPrice", priceRangeTop);
        }

        return params;
    }

    private void getManufactures(boolean show) {
        if (key == null) {
            rv.setRefreshing(false);
            return;
        }
        if (show) {
            rv.setRefreshing(true);
        }
        HttpManager.getInstance().post(getParams(), new BaseResponse<List<ManufacturersBean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<ManufacturersBean>> obj, List<ManufacturersBean> manufacturersBeans) {

                rv.setRefreshing(false);
                mDatas.clear();
                if (manufacturersBeans != null && !manufacturersBeans.isEmpty()) {
                    ManufacturersBean manufacturersBean = new ManufacturersBean(lastName);
                    manufacturersBean.setTop(true);
                    manufacturersBean.setBaseIndexTag("");
                    mDatas.add(manufacturersBean);
                    mDatas.addAll(manufacturersBeans);
                    setNewData(true, mDatas);
                }
            }

            @Override
            public void onFailure(NetError error) {
                rv.setRefreshing(false);
                super.onFailure(error);
            }
        });
    }

    private void searchName(String name) {
        if (mDatas == null || mDatas.isEmpty() || TextUtils.isEmpty(name)) {
            return;
        }
        name = name.trim();
        int size = mDatas.size();
        List<ManufacturersBean> temp = new ArrayList<>();
        ManufacturersBean bean = null;
        for (int a = 0; a < size; a++) {
            bean = mDatas.get(a);
            if (bean != null && bean.manufacturer != null && bean.manufacturer.contains(name)) {
                temp.add(bean);
            }
        }
        setNewData(false, temp);
    }

    private void setNewData(boolean isAll, List<ManufacturersBean> list) {
        /*

        if (isAll) {
            rv.getRecyclerView().removeItemDecoration(mDecoration);
            rv.addItemDecoration(mDecoration);
            if (!list.isEmpty()) {
                mIndexBar.setVisibility(View.VISIBLE);
            } else {
                mIndexBar.setVisibility(View.GONE);
            }
            mIndexBar.setmSourceDatas(list);//设置数据
            mDecoration.setmDatas(list);
            mIndexBar.requestLayout();
        } else {
            rv.getRecyclerView().removeItemDecoration(mDecoration);
            mIndexBar.setVisibility(View.GONE);
        }

        */
        adapter.setNewData(list);
    }


    public void show() {
        if (popWindow == null) {
            init();
        }
        if (mDatas.isEmpty()) {
        }
        getManufactures(true);
        popWindow.show();
    }

    public void dismiss() {
        if (popWindow != null) {
            popWindow.dismiss(false);
        }
    }

    public boolean isShow() {
        if (popWindow != null) {
            return popWindow.isShow();
        }
        return false;
    }

    public void setDataType(String key, String value, boolean isAvailable, boolean isPromotion, boolean isClassA, boolean isClassB
            , boolean isClassRx, boolean isClassElse, String spec, String shopCodes, String priceRangeFloor, String priceRangeTop, List<String> list) {

        this.key = key;
        this.value = value;
        this.isAvailable = isAvailable;
        this.isPromotion = isPromotion;
        this.isClassA = isClassA;
        this.isClassB = isClassB;
        this.isClassRx = isClassRx;
        this.spec = spec;
        this.shopCodes = shopCodes;
        this.priceRangeFloor = priceRangeFloor;
        this.priceRangeTop = priceRangeTop;
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        this.lastNames.addAll(list);

        mDatas.clear();
        if (etSearch != null) {
            etSearch.setText("");
        }
        adapter.setNewData(mDatas);
    }

    public void reset(boolean isBrand) {
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();

        if (isBrand) {
            key = "";
            value = "";
        }

        priceRangeFloor = "";
        priceRangeTop = "";
        drugClassification = "";

        isAvailable = false;
        isPromotion = false;

        isClassA = false;
        isClassB = false;
        isClassRx = false;
        isClassElse = false;

        adapter.notifyDataSetChanged();

    }

    private String setDrugsClass() {

        StringBuilder sb = new StringBuilder();
        if (isClassA) {
            sb.append("1").append(",");
        }
        if (isClassB) {
            sb.append("2").append(",");
        }
        if (isClassRx) {
            sb.append("3").append(",");
        }
        if (isClassElse) {
            sb.append("4").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

}
