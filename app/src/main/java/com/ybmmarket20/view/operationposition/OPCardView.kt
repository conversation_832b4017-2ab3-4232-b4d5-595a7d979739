package com.ybmmarket20.view.operationposition

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_MULTI_SHOP
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_SINGLE_SHOP
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.view.operationposition.content.SearchOPGoodsAdapter
import com.ybmmarket20.view.operationposition.content.goodsRecylerviewState.DynamicGoodsRecyclerViewState
import com.ybmmarket20.view.operationposition.content.goodsRecylerviewState.FixedGoodsRecyclerViewState
import com.ybmmarket20.view.operationposition.header.EmptyHeader
import com.ybmmarket20.view.operationposition.header.MultiShopHeader
import com.ybmmarket20.view.operationposition.header.SingleShopHeader

/**
 * 列表页运营位
 */
class OPCardView(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs) {

    private val opManager = OPCardViewManager.getInstance(this)

    var flowData: BaseFlowData? = null

    //商品曝光事件的回调
    var mTrackViewListener:( (rowsBean: RowsBean,productPosition:Int)->Unit)? = null
    //商品点击事件的回调
    var mTrackClickListener:( (rowsBean:RowsBean,productPosition:Int,isBtnClick:Boolean,btnContent:String,number:Int?)->Unit)? = null

    private var jGPageListCommonBean: JGPageListCommonBean? = null
    private var jgTrackBean:JgTrackBean? = null
    fun setData(info: OperationPositionInfo, parentPosition: Int,mJgTrackBean:JgTrackBean?,mJGPageListCommonBean: JGPageListCommonBean?) {
        jgTrackBean = mJgTrackBean
        jGPageListCommonBean = mJGPageListCommonBean
        opManager.initView()
        setHeader(info)
        setContentList(info, parentPosition)
    }

    /**
     * 设置头部
     */
    private fun setHeader(info: OperationPositionInfo) {
        when {
            isContainsSingleShopHeader(info) -> SingleShopHeader(info, opManager)
            isContainsMultiShopHeader(info) -> MultiShopHeader(info, opManager)
            else -> EmptyHeader()
        }.apply {
            handleVisibility()
            handleData()
        }
    }

    /**
     * 设置内容
     */
    private fun setContentList(info: OperationPositionInfo, parentPosition: Int) {
        val rv = opManager.getContentListView()
        rv.visibility = View.VISIBLE
        when (info.products.size) {
            3 -> FixedGoodsRecyclerViewState(rv, info)
            else -> DynamicGoodsRecyclerViewState(rv, info)
        }.apply {
            initRecyclerView()
            rv.isNestedScrollingEnabled = getNestedScrollingEnabled()
            addItemDivider()
        }
        val sOPAdapter = SearchOPGoodsAdapter(info.products.toMutableList(), info.showType == OPERATION_POSITION_TYPE_MULTI_SHOP, parentPosition).apply {
            trackViewListener = { rowsBean,productPosition ->
                mTrackViewListener?.invoke(rowsBean,productPosition)
            }

            trackClickListener = { rowsBean, productPosition,isBtnClick:Boolean,btnContent:String,number:Int? ->
                mTrackClickListener?.invoke(rowsBean,productPosition,isBtnClick, btnContent, number)
            }

            mJgTrackBean = jgTrackBean
            jGPageListCommonBean = <EMAIL>
        }
        sOPAdapter.flowData = flowData
        rv.adapter = sOPAdapter
    }

    /**
     * 是否包含单店铺头部
     */
    private fun isContainsSingleShopHeader(info: OperationPositionInfo): Boolean {
        return info.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP
    }

    /**
     * 是否包含多店铺头部
     */
    private fun isContainsMultiShopHeader(info: OperationPositionInfo): Boolean {
        return info.showType == OPERATION_POSITION_TYPE_MULTI_SHOP && !TextUtils.isEmpty(info.title)
    }

    /**
     * 设置卡片背景
     */
    fun setCardViewBg(resId: Int) {
        opManager.setCardViewBg(resId)
    }
}