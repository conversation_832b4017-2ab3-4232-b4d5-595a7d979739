package com.ybmmarket20.view;

import android.content.Context;
import androidx.annotation.Nullable;
import com.google.android.material.appbar.AppBarLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapGridLayoutManager;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.utils.UiUtils;

/**
 * 一键回到顶端
 */

public class MyFastScrollView extends RelativeLayout {

    private ImageView mIv;
    private AppBarLayout mAppbar;
    private RelativeLayout mContentView;
    private CommonRecyclerView mRecyclerView;

    private final float SHOW_FLOATINT_SCREEN_HEIGHT = 1.5f;

    private int mState, y, dy, screenHeight;
    boolean isShowFloating = false, isShowFastScroll = false;

    public MyFastScrollView(Context context) {
        this(context, null);
    }

    public MyFastScrollView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MyFastScrollView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    /**
     * 初始化
     *
     * @param context 上下文
     */
    private void init(Context context) {

        screenHeight = UiUtils.getScreenHeight();
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mContentView = (RelativeLayout) inflater.inflate(getLayoutId(), this, true);
        mIv = (ImageView) mContentView.findViewById(R.id.iv);
        mIv.setOnClickListener(itemClick);
    }

    public int getLayoutId() {
        return R.layout.view_my_fast_scroll;
    }

    private ItemClick itemClick = new ItemClick();

    private class ItemClick implements OnClickListener {
        @Override
        public void onClick(View v) {
            if (mRecyclerView == null) {
                return;
            }
            try {
                if (mAppbar != null) {
                    mAppbar.setExpanded(true, true);
                }
            } catch (Exception e) {
                BugUtil.sendBug(e);
            }

            mRecyclerView.getRecyclerView().stopScroll();
            if (mRecyclerView.getLayoutManager() instanceof WrapLinearLayoutManager) {
                UiUtils.MoveToPosition((WrapLinearLayoutManager) mRecyclerView.getLayoutManager(), 0);
            }else if (mRecyclerView.getLayoutManager() instanceof WrapGridLayoutManager){
                UiUtils.MoveToPosition((WrapGridLayoutManager) mRecyclerView.getLayoutManager(), 0);
            }
        }
    }

    /**
     * 是否显示一键返回顶部按钮
     *
     * @param y  滚动距离
     * @param dy 判断上下滚动状态
     */
    public void showFastScroll(int y, int dy) {

        if (y > screenHeight * SHOW_FLOATINT_SCREEN_HEIGHT) {
            if (!isShowFastScroll) {
                mIv.setVisibility(View.VISIBLE);
                isShowFastScroll = true;
            }
        } else {
            if (isShowFastScroll) {
                mIv.setVisibility(View.INVISIBLE);
                isShowFastScroll = false;
            }
        }
    }

    /**
     * @param recyclerView 当前列表view
     * @param y            滚动距离
     * @param dy           判断上下滚动状态
     * @param appbar       全部药品列表顶部toolBar
     */
    public void showFastScroll(CommonRecyclerView recyclerView, int y, int dy, AppBarLayout appbar) {
        this.mRecyclerView = recyclerView;
        this.mAppbar = appbar;
        this.y = y;
        this.dy = dy;
        showFastScroll(y, dy);
    }

    /**
     * @param state 滚动状态
     */
    public void showStateFastScroll(int state) {
        this.mState = state;
    }

}
