package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.SeekBar
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer
import com.videolive.AbstractVideoAllCallBack
import com.ybmmarket20.R
import kotlinx.android.synthetic.main.video_layout_commodity.view.*

/**
 * 详情页播放器
 */
class CommodityVideoView(context: Context, attr: AttributeSet?): StandardGSYVideoPlayer(context, attr) {

    companion object {
        //banner
        const val VIDEO_COMMODITY_TYPE_BANNER = 0
        //全屏
        const val VIDEO_COMMODITY_TYPE_FULL = 1
    }

    var videoType = VIDEO_COMMODITY_TYPE_BANNER
    private var voiceStatus = false
    var fullContainerBg: ViewGroup? = null
    var mFullContainer: ViewGroup? = null
    var mBannerContainer: ViewGroup? = null
    var switchBlock: ((Int) -> Unit)? = null
    var startClickBlock:(() -> Unit)? = null

    constructor(context: Context): this(context, null){
        initView()
    }

    init {
        initView()
    }

    private fun initView() {
//        GSYVideoManager.instance().isNeedMute = !voiceStatus
        switchToBanner()
        handleCbStatus()
        cb_banner.setOnClickListener(VoiceClickListener())
        cb_full.setOnClickListener(VoiceClickListener())
        setVideoAllCallBack(object : AbstractVideoAllCallBack() {
            override fun onStartPrepared(url: String?, vararg objects: Any?) {
                super.onStartPrepared(url, *objects)
                //处理播放完成后拖动进度条
                if (isCompleted) {
                    isCompleted = false
                    cancelProgressTimer()
                    postDelayed({
                        gsyVideoManager.seekTo(seekTime.toLong())
                        startProgressTimer()
                    }, 500)
                }
            }
        })
    }

    var isCompleted = false
    var seekTime: Int = 0

    override fun onStopTrackingTouch(seekBar: SeekBar?) {
        super.onStopTrackingTouch(seekBar)
        if (currentState == CURRENT_STATE_AUTO_COMPLETE) {
            isCompleted = true
            try {
                val time = seekBar!!.progress * duration / 100
                seekTime = time
                startPrepare()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    inner class VoiceClickListener: View.OnClickListener {
        override fun onClick(v: View?) {
            voiceStatus = !voiceStatus
            handleCbStatus()
            GSYVideoManager.instance().isNeedMute = !voiceStatus
        }

    }

    fun handleCbStatus() {
        cb_banner.isChecked = voiceStatus
        cb_full.isChecked = voiceStatus
        Log.i("handleCbStatus", "$voiceStatus")
    }

    override fun resolveUIState(state: Int) {
        super.resolveUIState(state)
        if (videoType == VIDEO_COMMODITY_TYPE_BANNER) {
            if (state != CURRENT_STATE_AUTO_COMPLETE) {
                setViewShowState(mStartButton, GONE)
            }
            setViewShowState(mBottomContainer, INVISIBLE)
        }
    }

    /**
     * 设置声音，设置开启和声音开关
     */
    fun setVoiceOpen(isOpen: Boolean) {
        cb_banner.isChecked = isOpen
        cb_full.isChecked = isOpen
        voiceStatus = isOpen
        GSYVideoManager.instance().isNeedMute = !isOpen
    }

    override fun touchDoubleUp() {

    }

    override fun onClickUiToggle() {
        if (videoType == VIDEO_COMMODITY_TYPE_FULL) {
            super.onClickUiToggle()
        } else {
            switchToFull()
        }
    }

    override fun getLayoutId(): Int = R.layout.video_layout_commodity

    override fun updateStartImage() {
        if (mStartButton is ImageView) {
            val imageView = mStartButton as ImageView
            if (mCurrentState == CURRENT_STATE_PLAYING) {
                imageView.setImageResource(R.drawable.video_click_pause_selector)
            } else if (mCurrentState == CURRENT_STATE_ERROR) {
                imageView.setImageResource(R.drawable.video_click_error_selector)
            } else {
                imageView.setImageResource(R.drawable.icon_commodity_play)
            }
        }
    }

    /**
     * 从全屏切换到banner
     */
    fun switchToBanner() {
        videoType = VIDEO_COMMODITY_TYPE_BANNER
        switchType(VIDEO_COMMODITY_TYPE_BANNER)
        mBottomProgressBar.visibility = View.VISIBLE
        startProgressTimer()
    }

    /**
     * 从banner切换到全屏
     */
    fun switchToFull() {
        val close = fullContainerBg?.findViewById<ImageView>(R.id.iv_video_close)
        close?.setOnClickListener {
            switchToBanner()
        }
        videoType = VIDEO_COMMODITY_TYPE_FULL
        switchType(VIDEO_COMMODITY_TYPE_FULL)
        startProgressTimer()
    }


    /**
     * 切换状态
     */
    private fun switchType(targetType: Int) {
        if (mFullContainer == null || mBannerContainer == null) return
        if (targetType == VIDEO_COMMODITY_TYPE_BANNER) {
            cb_banner.visibility = View.VISIBLE
            fullContainerBg?.visibility = GONE
            mFullContainer?.removeView(this)
            mBannerContainer?.addView(this)
        } else {
            cb_banner.visibility = View.GONE
            handleCbStatus()
            fullContainerBg?.visibility = View.VISIBLE
            mBannerContainer?.removeView(this)
            mFullContainer?.addView(this)
        }
        resolveUIState(currentState)
        if (currentState == CURRENT_STATE_PAUSE) {
            //暂停状态切换到Banner处理黑屏
            val time: Int = mProgressBar.progress * duration / 100
            gsyVideoManager.seekTo(time.toLong())
            postDelayed({
                setViewShowState(mStartButton, VISIBLE)
                if (targetType == VIDEO_COMMODITY_TYPE_BANNER) {
                    setViewShowState(mBottomProgressBar, VISIBLE)
                }
            }, 500)

        }
        switchBlock?.invoke(targetType)
    }

    override fun startButtonLogic() {
        super.startButtonLogic()
        startClickBlock?.invoke()
    }

    override fun onPrepared() {
        super.onPrepared()
        GSYVideoManager.instance().isNeedMute = !voiceStatus
    }

    /**
     * 释放资源
     */
    fun releaseAllVideos() {
        GSYVideoManager.releaseAllVideos()
    }

    /**
     * 点击返回按钮切换回Banner状态
     */
    fun onBackPress(): Boolean {
        val isBackPress = videoType == VIDEO_COMMODITY_TYPE_FULL
        if (isBackPress) switchToBanner()
        return isBackPress
    }

    fun setLifeCycle(lifeCycle: Lifecycle) {
        lifeCycle.addObserver(VideoLifeCycleObserver())
    }

    inner class VideoLifeCycleObserver: LifecycleObserver {

        @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
        fun onResume() {
            onVideoResume()
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
        fun onPause() {
            onVideoPause()
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        fun onDestroy() {
            onVideoReset()
        }
    }


}


